/* QUnit extension to test that two numeric values are 'close enough'.
 *
 * Translated to TypeScript from
 * https://github.com/jquery/qunit/blob/v1.11.0/addons/close-enough/qunit-close-enough.js */

import qunit = require("qunit");

/**
 * Checks that the first two arguments are equal, or are numbers close enough to be considered equal
 * based on a specified maximum allowable difference.
 *
 * @example close(3.141, Math.PI, 0.001);
 *
 * @param actual
 * @param expected
 * @param maxDifference the maximum inclusive difference allowed between the actual and expected numbers
 * @param message
 */
export function close(actual: number, expected: number, maxDifference: number, message?: string): void {
    var passes = (actual === expected) || Math.abs(actual - expected) <= maxDifference;
    (<any>qunit).push(passes, actual, expected, message);
}

export function notClose(actual: number, expected: number, minDifference: number, message?: string): void {
    var passes = (actual !== expected) && Math.abs(actual - expected) > minDifference;
    (<any>qunit).push(passes, actual, expected, message);
}