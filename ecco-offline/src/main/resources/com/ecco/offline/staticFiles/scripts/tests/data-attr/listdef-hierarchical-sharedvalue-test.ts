import qunit = require("qunit");

import $ = require("jquery")
import ListDefHierarchicalSharedValueSelectList = require("../../data-attr/ListDefHierarchicalSharedValueSelectList");
import featuresMock = require("./featuresMocks");

qunit.asyncTest("innermost child value saved", () => {
    var myLists = new ListDefHierarchicalSharedValueSelectList($(".listdef-hierarchical-select-list4"), featuresMock.featureRepository);

    qunit.expect(6);

    myLists.load().then( () => {
        var $country = $("#test_country2");
        var $language = $("#test_language2");
        var $dialect = $("#test_dialect2");
        var $sharedName = $("#sharedName");

        // check country has UK as first item (after '-')
        var firstItemValue = $country.find("option").eq(1).val();
        qunit.strictEqual(firstItemValue, "11", "loaded country data matched");

        // check language has 4 items ('-' + 3) since its parent has UK selected
        var itemsFound = $language.find("option").length;
        qunit.strictEqual(itemsFound, 4, "loaded language data matched");

        // check dialect has 1 items ('-') since its parent has nothing selected
        itemsFound = $dialect.find("option").length;
        qunit.strictEqual(itemsFound, 1, "loaded dialect data not present yet");

        // check sharedName has 11 UK initial value since that was the initial value
        var sharedNameValue = $sharedName.val();
        qunit.strictEqual(sharedNameValue, "11", "sharedName data set to initial data");

        // change language selection from '-' to english 1
        $language.find("select option:selected").prop('selected', false);
        $language.find("select option").filter('[value="1"]').prop('selected', true).trigger("change");

        // check sharedName has value 1 since language is changed to english 1
        sharedNameValue = $sharedName.val();
        qunit.strictEqual(sharedNameValue, "1", "sharedName set correctly");

        // change dialect selection from - to scouse 5
        $dialect.find("select option:selected").prop('selected', false);
        $dialect.find("select option").filter('[value="5"]').prop('selected', true).trigger("change");

        // check sharedName has value 5 since language is changed to scouse 1
        sharedNameValue = $sharedName.val();
        qunit.strictEqual(sharedNameValue, "5", "sharedName set correctly");

        qunit.start();
    });

});

qunit.asyncTest("innermost child value loaded", () => {
    var myLists = new ListDefHierarchicalSharedValueSelectList($(".listdef-hierarchical-select-list5"), featuresMock.featureRepository);

    qunit.expect(3);

    myLists.load().then( () => {
        var $country = $("#test_country3");
        var $language = $("#test_language3");
        var $dialect = $("#test_dialect3");

        // check country is UK 11
        var selectedValue = $country.find("option:selected").val();
        qunit.strictEqual(selectedValue, "11", "loaded country data matched");

        // check language is english 1
        var selectedValue = $language.find("option:selected").val();
        qunit.strictEqual(selectedValue, "1", "loaded language data matched");

        // check dialect is scouse 5
        var selectedValue = $dialect.find("option:selected").val();
        qunit.strictEqual(selectedValue, "5", "loaded dialect data matched");

        qunit.start();
    });

});

qunit.load();
qunit.start();
