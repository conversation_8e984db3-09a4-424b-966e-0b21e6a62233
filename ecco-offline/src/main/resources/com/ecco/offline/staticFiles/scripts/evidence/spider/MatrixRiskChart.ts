import {delay, StringUtils} from "@eccosolutions/ecco-common"
import * as dto from "ecco-dto/evidence-risk-dto";
import {ServiceRecipientWithEntities} from 'ecco-dto';
import RadarChartSpokes = require("../../draw/RadarChartSpokes");

import services = require("ecco-offline-data");

import BaseGraphControl = require("./BaseGraphControl");
import events = require("../events");
import domain = require("../risk/domain");
import RiskEvidenceSnapshot = domain.RiskEvidenceSnapshot;
import RiskWorkEvidenceDto = dto.RiskWorkEvidenceDto;

class BackingData {
    constructor(public serviceRecipient: ServiceRecipientWithEntities, public evidence: dto.RiskWorkEvidenceDto[]) {
    }
}

class MatrixRiskChart extends BaseGraphControl<BackingData> {
    private riskEvidenceSnapshot: RiskEvidenceSnapshot;
    private loaded = false;

    constructor(private serviceRecipientId?: number) {
        super();
        events.RiskUpdateEvent.bus.addHandler( (event) => this.handleUpdateEvent(event) );
    }

    protected override onActivated() {
        if (!this.loaded) {
            this.load();
        }
    }

    protected fetchViewData() {
        return services.getReferralRepository().findOneServiceRecipientWithEntities(this.serviceRecipientId)
            .then(svcRec =>
                services.getRiskWorkRepository().findRiskWorkByServiceRecipientId(this.serviceRecipientId)
                    .then( evidence => new BackingData(svcRec, evidence) )
                );
    }

    protected renderGraph(data: BackingData): void {
        this.riskEvidenceSnapshot = new RiskEvidenceSnapshot(data.serviceRecipient.configResolver.getServiceType());
        this.drawAxes(); // draw the axes even if needs assessment not done
        this.applyHistory(data.evidence);
    }

    private drawAxes() {
        const numSpokes = this.riskEvidenceSnapshot.getRiskAreaEvidences().length;

        const labelledSpokes = new RadarChartSpokes(this.paper, this.centreX, this.centreY, "#bbb", "1.5", numSpokes);
        this.riskEvidenceSnapshot.getRiskAreaEvidences().forEach( (re) => {
            const maxLabelLen = numSpokes < 22 ? 26 - numSpokes : 4; // shorter the more there are
            const shortTxt = StringUtils.abbreviateWords(re.getRiskGroup().getName(), maxLabelLen);
            const label = labelledSpokes.addSpoke(this.radius).addLabel(shortTxt);
        });
    }

    /** Apply history and redraw after each piece of work
      * BEWARE OF ORDER */
    private applyHistory(itemsByNewestFirst: RiskWorkEvidenceDto[]) {
        const handleItem = (i: number): Promise<void> => {
            if (i < itemsByNewestFirst.length) {
                this.applyWork(itemsByNewestFirst[i], i, itemsByNewestFirst.length);
                return delay(1000 / itemsByNewestFirst.length)   // 1000 = total animation time
                        .then(() => handleItem(i + 1));
            } else {
                this.loaded = true;
                return Promise.resolve(null);
            }
        };
        handleItem(0);
    }

    private applyWork(work: RiskWorkEvidenceDto, itemIndex: number, itemCount: number ) {
        const isLastItem = itemIndex == itemCount - 1;
        work.riskActions.forEach( (action) => this.riskEvidenceSnapshot.addRiskActionEvidence(action) );
        if (itemIndex == 0 || isLastItem || this.isModernBrowser()) {
            this.redraw();
        }
    }

    public drawSpider() {
        const numSpokes = this.riskEvidenceSnapshot.getRiskAreaEvidences().length;

        const matrixLines = new RadarChartSpokes(this.paper, this.centreX, this.centreY, "black", "2.5", numSpokes);

        this.riskEvidenceSnapshot.getRiskAreaEvidences().forEach( (re) => {
            this.addDataPoint(re.getMatrixScore(), matrixLines);
        });
    }

    private addDataPoint(matrixScore: number, radialLines: RadarChartSpokes) {
        // TODO: Warning .. we should be picking which spoke!
        // Instead we're just going round-robin and order may be wrong
        const colour = (matrixScore <= 5) ? 'green' :
                (matrixScore < 15) ? 'yellow' :
                        'red';
        const percent = matrixScore * 4; // 25 max, x 4 = 100%
        radialLines.addSpoke(percent, colour);
    }

    private handleUpdateEvent(event: events.RiskUpdateEvent) {
        if (!this.riskEvidenceSnapshot) {
            return; // not loaded yet so no need to update
        }
        this.riskEvidenceSnapshot.applyCommand(event.command);
        this.redraw();
    }

    private redraw() {
        this.clear();
        this.drawAxes();
        this.drawSpider();
    }

    /* TODO
    public static showInModal(serviceRecipientId: number,   : number, serviceTypeId: number) {
        var modal = new Modal("modal-full");
        var form = new SupportRadarChart(serviceRecipientId, serviceTypeId);
        modal.popView(form);
        form.load();
    }
    */

}
export = MatrixRiskChart;
