package com.ecco.dom;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Assert;
import org.junit.Test;

import java.util.Locale;

public class JodaTimeZoneTest {

    DateTimeFormatter fmt = DateTimeFormat.fullDateTime();

    @Test
    public void testJodaDateAsFudgedUtc() {
        // construct a known date
        DateTime instance = new DateTime(2009, 11, 20, 13, 16, 43, 55, DateTimeZone.forID("Europe/London"));
        DateTime instanceAsUtc = instance.withZone(DateTimeZone.UTC);
        String instanceAsUtcPrint = fmt.withLocale(Locale.UK).print(instanceAsUtc);

        // now we need to fudge this to pretend that instance was actually a UTC
        // see PredicateSupport
        DateTime expected = new DateTime(2009, 11, 20, 13, 16, 43, 55, DateTimeZone.UTC);

        String expectedPrint = fmt.withLocale(Locale.UK).print(expected);

        Assert.assertEquals(expectedPrint, instanceAsUtcPrint);
    }

}
