import {
    createStyles,
    grey,
    makeStyles,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow
} from "@eccosolutions/ecco-mui";
import {
    EventResourceDto,
    EvidenceGroup,
    InvoiceLineDto,
    ReferralSummaryDto,
    SupportWork
} from "ecco-dto";
import * as React from "react";
import {useEffect, useState} from "react";
import {useServicesContext} from "../../ServicesContext";
import {TempInvoiceLineDto} from "../service/dataservice";
import {MainRows} from "./EvidenceView";

function InvoiceLineRow(props: {
    line: InvoiceLineDto, disabled: boolean, onClick: () => Promise<void>, hasWork: boolean
}) {
    const classes = useStyles();

    const [evidence, setEvidence] = useState<SupportWork | null>(null);
    const [calendarEvent, setCalendarEvent] = useState<EventResourceDto | null>(null);
    const [referral, setReferral] = useState<ReferralSummaryDto | null>(null);

    const {sessionData, calendarRepository, referralRepository, supportWorkRepository} =
        useServicesContext();

    const {line} = props;

    function loadEventData(eventId: string) {
        return calendarRepository
            .fetchEventsById([eventId])
            .then(events => setCalendarEvent(events[0]));
    }

    function getRelatedData(line: InvoiceLineDto) {
        const getWork = line.workUuid
            ? supportWorkRepository
                  .findOneSupportWorkByWorkUuid(
                      line.serviceRecipientId,
                      EvidenceGroup.needs,
                      line.workUuid
                  )
                  .then(work => {
                      setEvidence(work);
                  })
            : Promise.resolve();
        return getWork
            .then(() => {
                if (line.eventId) {
                    loadEventData(line.eventId);
                }
            })
            .then(() =>
                referralRepository().findOneReferralSummaryByServiceRecipientIdUsingDto(
                    line.serviceRecipientId
                )
            )
            .then(referral => setReferral(referral));
    }

    useEffect(() => {
        getRelatedData(line);
    }, [line.workUuid, evidence?.eventId]);

    const rateId = calendarEvent?.eventStatusRateId;
    //const rate = rateId ? sessionData.getListDefinitionEntryById(rateId).getDisplayName() : "";

    return (
        <TableRow className={!line.invoiceId ? classes.unverifiedLineItem : undefined}>
            {/*<TableCell>
                <Button
                    variant="contained"
                    color={line.reverseCharge ? "primary" : "secondary"}
                    disabled={disabled}
                    // TODO: this is badly abstracted. ReverseLineItem needs consideration post demo
                    onClick={onClick}
                >
                    {line.reverseCharge ? "include" : "reverse"}
                </Button>
            </TableCell>*/}
            <TableCell component="th" scope="row" padding="none">
                {line.workUuid}
            </TableCell>
            {/*<TableCell>{line.reverseCharge ? "Yes" : "No"}</TableCell>*/}
            <MainRows
                line={line}
                thresholdMins={0}
                noColourCode={true}
                evidence={evidence}
                calendarEvent={calendarEvent}
                plannedResource={line.plannedResourceName}
                referral={referral}
                sessionData={sessionData}
            />
            {/*TODO: Feature toggle this as summary <TableCell>{evidence?.minsTravel}</TableCell>*/}
            {/*<TableCell>{evidence?.mileageTo}</TableCell>*/}
            {/*<TableCell>{evidence?.mileageDuring}</TableCell>*/}
            {/*{hasWork && <TableCell>{evidence?.signatureId ? "Yes" : "No"}</TableCell>}*/}
            {/*{hasWork && <TableCell>{"rota visit"}</TableCell>}*/}
            {/*<TableCell>{line.type}</TableCell>
        {hasWork && <TableCell>{evidence?.locationId}</TableCell>}*/}
            {/*<TableCell>{calendarEvent?.title}</TableCell>*/}
            {/*<TableCell>{evidence?.authorDisplayName}</TableCell>*/}
            {/*<TableCell>{rate}</TableCell>*/}
            <TableCell>{line.invoiceId}</TableCell>
        </TableRow>
    );
}

function NestedInvoiceTable(props: {
    lines: InvoiceLineDto[];
    reverseLineItem: (lineItem: TempInvoiceLineDto, invoiceId: number) => Promise<void>;
}) {
    const classes = useStyles();
    const {lines} = props;
    // takes a line item and returns if it is reversable or not
    function isReversable(_invoiceLineItem: InvoiceLineDto) {
        return false;
        //return invoice.status === InvoiceStatus.DRAFT; // TODO: && !invoiceLineItem.verified;
    }
    const hasWork = lines?.findIndex(r => r.workUuid != null) >= 0;
    return (
        <Table className={classes.table}>
            <TableHead>
                <TableRow>
                    {/*<TableCell>actions</TableCell>*/}
                    <TableCell>w-id</TableCell>
                    {/*<TableCell>charge reversed</TableCell>*/}
                    <TableCell>client</TableCell>
                    <TableCell>title</TableCell>
                    <TableCell>planned (p)</TableCell>
                    <TableCell>actual (a)</TableCell>
                    <TableCell>p / a (mins)</TableCell>
                    <TableCell>reason</TableCell>
                    <TableCell>comment</TableCell>
                    {/*<TableCell>travel time (mins)</TableCell>*/}
                    {/*<TableCell>mileage to/from</TableCell>*/}
                    {/*<TableCell>mileage during visit</TableCell>*/}
                    {/*{hasWork && <TableCell>signed</TableCell>}
                    <TableCell>task</TableCell>*/}
                    {/*{hasWork && <TableCell>type</TableCell>}
                    {hasWork && <TableCell>location</TableCell>}*/}
                    {/*<TableCell>event title</TableCell>*/}
                    {/*<TableCell>author</TableCell>*/}
                    {/*<TableCell>rate</TableCell>*/}
                    <TableCell>invoice</TableCell>
                </TableRow>
            </TableHead>
            <TableBody className={classes.tableBody}>
                {lines?.map((line: InvoiceLineDto) => (
                    <InvoiceLineRow
                        key={line.workUuid}
                        line={line}
                        disabled={isReversable(line)}
                        onClick={() =>
                            props.reverseLineItem(line as TempInvoiceLineDto, line.invoiceId)
                        }
                        hasWork={hasWork}
                    />
                ))}
            </TableBody>
        </Table>
    );
}

const useStyles = makeStyles(() => createStyles({
    dateFilterElement: {
        margin: 20
    },
    button: {
        margin: 20
    },
    table: {
        zoom: '90%',
        minWidth: 800,
        width: '100%'
    },
    tableBody: {
        backgroundColor: 'white'
    },
    tableWrapper: {
        overflowX: 'auto'
    },
    tableCell: {
        padding: '5px'
    },
    unverifiedLineItem: {
       backgroundColor: grey[200]
    }
}));
export default NestedInvoiceTable;
