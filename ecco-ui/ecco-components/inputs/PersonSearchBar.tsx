import * as React from "react";
import {Component, FC, useState} from "react";
import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {
    Box,
    createStyles,
    FormHelperText,
    IconButton,
    InputBase,
    makeStyles,
    MenuItem,
    Paper,
    Theme
} from "@eccosolutions/ecco-mui";
import {PersonSearchCriteriaDto} from "ecco-dto/client-dto";
import {IconMenu} from "@eccosolutions/ecco-mui-controls";
import MenuIcon from "@material-ui/icons/Menu";
import SearchIcon from "@material-ui/icons/Search";
import BackspaceIcon from "@material-ui/icons/Backspace";

export function hasDigits(str: string): boolean {
    return /\d+/.test(str);
}

// This generates the styles, and then we pass them in to the components using the classes key
const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        root: {
            padding: "2px 4px",
            display: "block",
            alignItems: "center",
            width: "auto",
            maxWidth: 768,
            margin: "8px 0 4px",
        },
        inputRoot: {
            marginLeft: theme.spacing(1),
            flex: 1,
        },
        inputInput: {
            boxShadow: "none",
        },
        inputFocused: {
            boxShadow: "xnone",
            borderBottom: "xsolid 2px #3c83ca",
            borderRadius: 0
        },
        iconButton: {
            padding: 10,
        },
        errorTextRoot: { // use as classes=({root: classes.errorTextRoot}}
            margin: "4px 56px",
        },
      }),
);

export function SearchInput(props: {
    resultName: string
    placeholder: string
    value: string
    onValueChange: (value: string) => void
    onClickSearchBack: () => void
    onKeyDown: (event: React.KeyboardEvent) => void
    searchActive: boolean
    errorText: string | null
    iconMenu?: React.ReactElement
}) {
    const classes = useStyles();

    return <Box mx={0} my={2}>
        <Paper component="div" className={classes.root}>
            <Box display="flex">
                {props.iconMenu}
                <InputBase
                    classes={{root: classes.inputRoot, input: classes.inputInput, focused: classes.inputFocused}}
                    autoComplete="none"
                    autoFocus={true}
                    placeholder={props.placeholder}
                    inputProps={{"aria-label": props.placeholder}}
                    onChange={event => props.onValueChange((event.target as HTMLInputElement).value)}
                    value={props.value}
                    onKeyDown={props.onKeyDown}
                />
                <IconButton color="primary" className={classes.iconButton} aria-label="search"
                            id="search"
                            disabled={!!props.errorText}
                            onClick={event => {
                                props.onClickSearchBack();
                                event.preventDefault();
                            }}
                >
                    {props.searchActive ? <BackspaceIcon/> : <SearchIcon/>}
                </IconButton>
            </Box>
            {props.errorText &&
            <FormHelperText classes={{root: classes.errorTextRoot}} error>{props.errorText}</FormHelperText>}
        </Paper>
    </Box>;
}

interface SearchType {
    placeholder: string
    validate(inputText: string): string | null
    getSearchCriteria(searchText: string): PersonSearchCriteriaDto
}

export interface SearchProps<T> {
    resultName: string
    criteria: T
    initialSearchText?: string
    onTrigger: (criteria: T | null, enterPressed: boolean) => void
    searchActive: boolean  // TODO: "search" | "searching..." | "reset" (and disable when searching)
}

type SearchState = {
    searchTypeName: string
    searchText: string
    criteria: PersonSearchCriteriaDto | null
    errors: string | null
};

type IconMenuProps = {
    searchTypeName: string
    onSearchChange: (searchTypeName: string) => void
}

const SearchMenu: FC<IconMenuProps> = props => {
    const [menuOpen, setMenuOpen] = useState(false);

    const onMenuChoice = (type: string) => {
        props.onSearchChange(type);
        setMenuOpen(false);
    }

    return <IconMenu id="search-type-menu" iconComponent={<MenuIcon/>}
                               onClick={() => setMenuOpen(true)}
                               open={menuOpen}
                               onClose={() => setMenuOpen(false)}>
        <MenuItem
            title={"search by name"}
            selected={props.searchTypeName == "name"}
            onClick={() => onMenuChoice("name")}
        >client name</MenuItem>
        <MenuItem title={"search by address"}
                  selected={props.searchTypeName == "address"}
                  onClick={() => onMenuChoice("address")}
        >address</MenuItem>
        <MenuItem title={"search by postcode"}
                  selected={props.searchTypeName == "postcode"}
                  onClick={() => onMenuChoice("postcode")}
        >postcode</MenuItem>
    </IconMenu>;

}

export class PersonSearchBar extends Component<SearchProps<PersonSearchCriteriaDto>, SearchState> {
    private handlers: StringToObjectMap<SearchType> = {
        name: {
            placeholder: `first last (J S, J Smith, John S) or ${this.props.resultName} id`,
            validate: inputText => {
                if (!inputText) {
                    return null; // Otherwise "required" but for search form this is obvious
                }
                const parts = inputText.split(" ");
                if (parts.length == 2 || hasDigits(parts[0])) {
                    return null;
                }
                return "please include the last name too (e.g. Smith or Sm)";
            },
            getSearchCriteria(searchText: string): PersonSearchCriteriaDto {
                const parts = searchText.trim().split(" ");
                return parts.length == 1 && hasDigits(parts[0])
                    ? {
                          code: parts[0]
                      }
                    : {
                          firstName: parts.length > 1 ? parts[0] : "",
                          lastName: parts.length > 1 ? parts[1] : parts[0]
                      };
            }
        },
        address: {
            placeholder: `first line of address (e.g. 10 Downi)`,
            validate: () => null, // could validate length
            getSearchCriteria(line1: string): PersonSearchCriteriaDto {
                return {
                    address: {address: [line1]}
                };
            }
        },
        postcode: {
            placeholder: `first part of postcode (e.g. SW1)`,
            validate: () => null, // could validate length
            getSearchCriteria(postcode: string): PersonSearchCriteriaDto {
                return {
                    address: {postcode}
                };
            }
        }
    };

    constructor(props: SearchProps<PersonSearchCriteriaDto>) {
        super(props);
        const initialHandler = "name";
        const initialText = props.initialSearchText || "";

        const errors = this.handlers[initialHandler].validate(initialText);
        this.state = {
            searchTypeName: initialHandler,
            searchText: initialText,
            errors,
            criteria: errors ? null : this.handlers[initialHandler].getSearchCriteria(initialText)
        };
    }

    public isValid(): boolean {
        return !this.state.errors;
    }

    private handleCriteriaChange(searchText: string) {
        const errors = this.getHandler().validate(searchText.trim());

        const criteria = errors ? null : this.getHandler().getSearchCriteria(searchText);

        this.setState({searchText, criteria, errors});
    }

    private onTypeChange = (searchTypeName: string) => {
        this.setState({searchTypeName});
        this.handleCriteriaChange(this.state.searchText);
    };

    private handleSearchKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === "Enter") {
            event.preventDefault();
            if (this.isValid()) {
                this.props.onTrigger(
                    this.getHandler().getSearchCriteria(this.state.searchText),
                    true
                );
            }
        } else if (event.key === "Escape") {
            event.preventDefault();
            this.setState({searchText: ""});
            this.handleCriteriaChange("");
            this.props.onTrigger(null, false);
        }
    };

    /** Clicking the search/back icon */
    private onClick = () => {
        this.props.onTrigger(this.state.criteria, false);
    };

    override render() {
        const iconMenu = (
            <SearchMenu
                searchTypeName={this.state.searchTypeName}
                onSearchChange={this.onTypeChange}
            />
        );

        return (
            <SearchInput
                resultName={this.props.resultName}
                placeholder={this.getHandler().placeholder}
                iconMenu={iconMenu}
                value={this.state.searchText}
                onValueChange={value => this.handleCriteriaChange(value)}
                onKeyDown={this.handleSearchKeyDown}
                onClickSearchBack={this.onClick}
                searchActive={this.props.searchActive} // {this.props.searchActive ? "reset" : "search"}
                errorText={this.state.errors}
            />
        );
    }

    private getHandler() {
        return this.handlers[this.state.searchTypeName];
    }
}