// matches SRAddressLocationChangeCommandViewModel
import {
    BaseServiceRecipientCommandDto,
    BooleanChange,
    Mergeable,
    NumberChangeOptional,
    StringChangeOptional,
    UpdateCommandDto
} from "ecco-dto";
import {
    assertNotNull,
    BaseUpdateCommand,
    BaseUpdateCommandTransitioning
} from "../cmd-queue/commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EccoDateTime} from "@eccosolutions/ecco-common";

type Operation = "add" | "update" | "remove";

// Matches ServiceRecipientAddressLocationChangeCommandViewModel.java
export interface AddressHistoryCommandDto extends BaseServiceRecipientCommandDto {
    /** indicates whether this originates from an external system */
    externalSource?: boolean;

    id: number | null;

    operation: string;

    validFrom: StringChangeOptional;

    contactId?: number;

    addressLocation?: NumberChangeOptional;

    buildingLocation?: NumberChangeOptional;
}

/**
 * Command to add, remove or update
 * Matches SRAddressLocationChangeCommandViewModel.java
 */
export class AddressHistoryCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "addressLoc";

    /** indicates whether this originates from an external system */
    externalSource?: boolean;

    private validFrom: StringChangeOptional;
    private addressLocation?: NumberChangeOptional;
    private buildingLocation?: NumberChangeOptional;

    /** operation should be either "update" or "delete" */
    constructor(
        commandUuid: Uuid,
        private operation: Operation,
        private serviceRecipientId: number,
        private id: number | null,
        private contactId?: number
    ) {
        super(
            `service-recipients/${serviceRecipientId}/address-location/command/`,
            commandUuid,
            AddressHistoryCommand.discriminator
        );
        assertNotNull(commandUuid, "AddressHistoryCommand.uuid");
        // default to now to allow normal operation
        this.changeValidFrom(null, EccoDateTime.nowLocalTime());
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    public changeAddressLocation(from: number | null, to: number) {
        this.addressLocation = this.asNumberChange(from, to);
        return this;
    }

    public changeBuildingLocation(from: number | null, to: number) {
        this.buildingLocation = this.asNumberChange(from, to);
        return this;
    }

    public changeValidFrom(from: EccoDateTime | null, to: EccoDateTime) {
        this.validFrom = this.asLocalDateTimeChange(from, to);
        return this;
    }

    public override hasChanges(): boolean {
        return (
            this.buildingLocation != null ||
            this.addressLocation != null ||
            this.validFrom != null ||
            this.operation == "add" ||
            this.operation == "remove"
        );
    }

    protected getCommandDto(): AddressHistoryCommandDto {
        const dto = {
            operation: this.operation,
            commandName: this.commandName,
            id: this.id,
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            validFrom: this.validFrom,
            contactId: this.contactId,
            buildingLocation: this.buildingLocation,
            addressLocation: this.addressLocation
        } as AddressHistoryCommandDto;
        if (this.externalSource) {
            dto.externalSource = true;
        }
        return dto;
    }
}


export interface AddressChangeDto extends UpdateCommandDto {
    addressLocationId?: number;
    disabled?: BooleanChange;
}

/**
 * Command to add, remove or update
 */
export class AddressChangeCommand extends BaseUpdateCommand {
    private disabled?: BooleanChange;

    /** serviceRecipientId is omitted if creating a new one */
    constructor(private operation: "update", private addressLocationId?: number) {
        super("buildings/address-location/commands/");
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    public changeDisabled(from: boolean, to: boolean) {
        this.disabled = this.asBooleanChange(from, to);
        return this;
    }

    public override hasChanges(): boolean {
        return this.disabled != null;
    }

    public toDto(): AddressChangeDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            addressLocationId: this.addressLocationId,
            operation: this.operation,
            disabled: this.disabled
        };
    }
}
