///<reference path="../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import * as dto from "ecco-dto";
import {ReferralDto, SessionData} from "ecco-dto";
import * as types from "@eccosolutions/ecco-common";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import Lazy = require("lazy");
import reportDto = require("./dto");
import analysisTypes = require("./analysis/types");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {SupportAction, SupportWork, BaseWork} from "ecco-dto";
import Group = analysisTypes.Group;
import ReferralAggregate = analysisTypes.ReferralAggregate;
import {getGlobalEccoAPI} from "ecco-components";
import {AnalysisContext} from "./chart-domain";

/*
 * Analysers can be based on just working through a stream, but also could be doing
 * statistical analysis, and possibly using lookups from other data sources.  For example,
 * a Social Return on Investment analyser would be provided with a map of financial proxies per outcome area,
 * and the total budget that was spent.
 */

interface Accumulator<T, ITEM> {
    reduce(items: Sequence<ITEM>): T;
}

function messageLookup(key: string) {
    const messages = getGlobalEccoAPI().sessionData.getMessages();
    // @ts-ignore
    return messages[key] || "[" + key + "]";
}

export interface ReferralReportItem extends ReferralAggregate {}

/* TODO: Refactor this...
[13:17:28] Dan: analysers should return a collection<T> where T is some type specific to each analyser, and then you can map that type to a row in a table, or an entry in a chart, or whatever
Here I mean “map” in the sense of having one or more mapping functions T -> U
*/
export interface KeyedResultItem extends Group<ReferralAggregate> {}

/** A work item where we've added a reference to the parent referral while doing map() etc processing */
interface WorkWithRefToReferral extends BaseWork {
    /** parent referral */
    referral: ReferralDto;
}

/** A work item where we've added a reference to the parent ReportItem while doing map() etc processing */
interface WorkWithRefToReportItem extends BaseWork {
    /** parent item */
    reportItem: ReferralAggregate;
}

export interface SmartStepCountsAnalysis extends KeyedResultItem {
    totalAchievedSmartSteps: number;
    totalOutstandingSmartSteps: number;
}

export interface VisitsAnalysis extends KeyedResultItem {
    latestWorkDate: EccoDate | null;
    totalTimeSpentMins: number;
    totalVisits: number;
    countByCommentType: {[key: string]: number};
}

export interface DaysOfWeekAnalysis extends KeyedResultItem {
    sunday: number;
    monday: number;
    tuesday: number;
    wednesday: number;
    thursday: number;
    friday: number;
    saturday: number;
}

interface ItemsEntry<T> {
    key: string;
    items: T[];
}
export interface ReferralItemsEntry extends ItemsEntry<ReferralAggregate> {}

interface GroupFn<T> {
    (input: Sequence<T>): Sequence<ItemsEntry<T>>;
}

/** e.g. take ages, and quantise into 0-7, 8-15, 16-17, 18-64, 65 and over */
interface QuantiserFn {
    (value: number): string;
}

function defaultAgeGroupQuantiser(value: number): string {
    if (value < 16) {
        return "under 16";
    }
    if (value < 18) {
        return "16-17";
    }
    if (value < 25) {
        return "18-24";
    }
    if (value < 65) {
        return "25-64";
    }
    return "65 and over";
}

function getQuantisedAgeInYearsAtTimeOfReferral(item: ReferralAggregate): string {
    if (!item.client!.birthDate) {
        return "no birth date";
    }
    if (!item.referral.receivedDate) {
        return "no referral date";
    }
    const birthDate = EccoDate.parseIso8601(item.client!.birthDate)!;
    const referralDate = EccoDate.parseIso8601(item.referral.receivedDate);
    let diff = referralDate.toUtcJsDate().getTime() - birthDate.toUtcJsDate().getTime();
    diff = diff / (1000 * 3600 * 24 * 365.25); // stand a chance of dealing safely with leap-day births
    return defaultAgeGroupQuantiser(diff);
}

function ensureWorkReferencesItem(
    item: ReferralAggregate,
    work: BaseWork
): WorkWithRefToReportItem {
    const result = <WorkWithRefToReportItem>work;
    result.reportItem = item;
    return result;
}

/** Produce a sequence of pairs of referral, work such that they can be grouped by entries relating to work */
function flattenWork(input: Sequence<ReferralAggregate>): Sequence<WorkWithRefToReportItem> {
    const allWork: Sequence<WorkWithRefToReportItem> = input
        .filter(work => work != null)
        .map(item => item.supportWork!.map(work => ensureWorkReferencesItem(item, work)))
        .flatten<WorkWithRefToReportItem>();
    return allWork;
}

function groupByAgeAtDateOfReferral(
    input: Sequence<ReferralAggregate>
): Sequence<ItemsEntry<ReferralAggregate>> {
    return input
        .groupBy(inputElement => getQuantisedAgeInYearsAtTimeOfReferral(inputElement))
        .pairs()
        .map(group => ({key: group[0], items: group[1]}));
}

/** Group work by the person who did the work (author) */
function groupByEvidenceAuthor(
    input: Sequence<ReferralAggregate>
): Sequence<ItemsEntry<WorkWithRefToReportItem>> {
    const work = flattenWork(input);
    return work
        .groupBy(inputElement => inputElement.authorDisplayName || "no author assigned")
        .pairs()
        .map(group => ({key: group[0], items: group[1]}));
}

function groupByReferralAssignedWorker(
    input: Sequence<ReferralAggregate>
): Sequence<ItemsEntry<ReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                // TODO: Must do by workerId but that needs adding at server side
                //            inputElement.referral.workerId ? inputElement.referral.workerId.toString() : "(no worker)")
                inputElement.referral.supportWorkerDisplayName || "no worker assigned"
        )
        .pairs()
        .map(group => ({key: group[0], items: group[1]}));
}

function groupByReferralProjectName(
    input: Sequence<ReferralAggregate>,
    sessionData: SessionData
): Sequence<ItemsEntry<ReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                sessionData.getServiceCategorisation(inputElement.referral.serviceAllocationId)
                    .projectName || "no current project assigned"
        )
        .pairs()
        .map(group => ({key: group[0], items: group[1]}));
}

function countsOfNumberOfReferrals(
    key: string,
    input: Sequence<ReferralAggregate>
): KeyedResultItem {
    return {
        key: key,
        count: input.size(),
        elements: input
    };
}

function countsOfDayAttending(key: string, input: Sequence<ReferralAggregate>): DaysOfWeekAnalysis {
    function applyAnalysis(prev: DaysOfWeekAnalysis, item: ReferralAggregate): DaysOfWeekAnalysis {
        prev.monday += item.referral.daysAttending & dto.DAYS_AS_BITS.MON ? 1 : 0;
        prev.tuesday += item.referral.daysAttending & dto.DAYS_AS_BITS.TUE ? 1 : 0;
        prev.wednesday += item.referral.daysAttending & dto.DAYS_AS_BITS.WED ? 1 : 0;
        prev.thursday += item.referral.daysAttending & dto.DAYS_AS_BITS.THUR ? 1 : 0;
        prev.friday += item.referral.daysAttending & dto.DAYS_AS_BITS.FRI ? 1 : 0;
        prev.saturday += item.referral.daysAttending & dto.DAYS_AS_BITS.SAT ? 1 : 0;
        prev.sunday += item.referral.daysAttending & dto.DAYS_AS_BITS.SUN ? 1 : 0;
        return prev;
    }

    const memo: DaysOfWeekAnalysis = {
        key: key,
        sunday: 0,
        monday: 0,
        tuesday: 0,
        wednesday: 0,
        thursday: 0,
        friday: 0,
        saturday: 0,
        elements: input
    };
    return input.reduce((prev, item) => applyAnalysis(prev, item), memo);
}

/** flatten and group by related activity interests */
function groupByActivityInterest(input: Sequence<ReferralAggregate>): Sequence<DaysOfWeekAnalysis> {
    const flattened = input
        .map(item => {
            return item.activityInterest!.map(interest => {
                return {key: interest.name, item: item};
            });
        })
        .flatten<{key: string; item: ReferralAggregate}>();
    const grouped = flattened
        .groupBy(inputElement => inputElement.key)
        .pairs()
        .map(group => ({key: group[0], items: group[1].map(withKey => withKey.item)}));
    return grouped.map(entry => countsOfDayAttending(entry.key, Lazy(entry.items)));
}

// Example of ECCO-960
export function caseLoadFromSmartStepsByWorker(
    input: Sequence<ReferralAggregate>
): Sequence<SmartStepCountsAnalysis> {
    return caseLoadFromSmartStepsReport(input, groupByReferralAssignedWorker);
}

export function caseLoadFromSmartStepsReport(
    input: Sequence<ReferralAggregate>,
    groupFn: GroupFn<ReferralAggregate>
): Sequence<SmartStepCountsAnalysis> {
    return groupFn(input).map(pair => {
        const elements: Sequence<ReferralAggregate> = Lazy(pair.items);
        const allActions: Sequence<SupportAction> = elements
            .map(input => input.supportWork)
            .flatten<SupportWork>()
            .map(supportWork => supportWork.actions)
            .flatten<SupportAction>()
            .filter(action => action != null);
        const result: SmartStepCountsAnalysis = {
            key: pair.key,
            totalAchievedSmartSteps: allActions.filter(action => action.status == 3).size(),
            totalOutstandingSmartSteps: allActions.filter(action => action.status != 3).size(),
            elements: elements,
            count: pair.items.length
        };
        return result;
    });
}

interface VisitsAnalysisWIP extends VisitsAnalysis {
    uniqueItems: types.SparseArray<ReferralAggregate>;
}

function accumulateBaseWorkToVisitsAnalysis<T extends VisitsAnalysis>(
    prev: T,
    work: BaseWork,
    ctx: AnalysisContext
): T {
    const workDate = EccoDateTime.parseIso8601(work.workDate).toEccoDate();
    if (!prev.latestWorkDate || workDate.laterThan(prev.latestWorkDate)) {
        prev.latestWorkDate = workDate;
    }
    prev.totalTimeSpentMins += work.minsSpent;
    prev.totalVisits += 1;

    let commentType = "-";
    if (work.commentTypeId) {
        const type = ctx.getSessionData().getListDefinitionEntryById(work.commentTypeId);
        commentType = type.getDisplayName();
    }
    if (prev.countByCommentType[commentType]) {
        prev.countByCommentType[commentType]++;
    } else {
        prev.countByCommentType[commentType] = 1;
    }
    return prev;
}

/** Accumulate analysis where we've flattened to Work items where we then need to deduplicate the
 * related ReportItems for the next stage
 */
class VisitsAnalysisFromWorkAccumulator
    implements Accumulator<VisitsAnalysis, WorkWithRefToReportItem>
{
    private readonly memo: VisitsAnalysisWIP;

    constructor(key: string, private ctx: AnalysisContext) {
        this.memo = {
            key: key,
            latestWorkDate: null,
            totalTimeSpentMins: 0,
            totalVisits: 0,
            countByCommentType: {},
            uniqueItems: {},
            elements: <Sequence<ReferralAggregate>>Lazy([]) // will map from unique items when done
        };
    }

    private accumulate(prev: VisitsAnalysisWIP, work: WorkWithRefToReportItem): VisitsAnalysisWIP {
        prev.uniqueItems[work.reportItem.referral.referralId] = work.reportItem;
        return accumulateBaseWorkToVisitsAnalysis(prev, work, this.ctx);
    }

    private postProcess(): void {
        this.memo.elements = <Sequence<ReferralAggregate>>Lazy(this.memo.uniqueItems).values();
    }

    reduce(allWork: Sequence<WorkWithRefToReportItem>): VisitsAnalysis {
        const result = allWork.reduce((prev, work) => this.accumulate(prev, work), this.memo);
        this.postProcess();
        return result;
    }
}

/*
// removed as ctx may not be provided, but this seems unused anyway
function visitsBreakdownByAuthor(input: Sequence<ReferralAggregate>, ctx: AnalysisContext): Sequence<VisitsAnalysis> {
    return groupByEvidenceAuthor(input)
        .map((pair) => {
            const allWork: Sequence<WorkWithRefToReportItem> = Lazy(pair.items);
            const sessionData = pair.items.length > 0 ? pair.items[0].reportItem.sessionData : null;
            return new VisitsAnalysisFromWorkAccumulator(pair.key, ctx).reduce(allWork);
        });
}
*/

/*
// removed as ctx may not be provided, but this seems unused anyway
function visitsBreakdownByReferral(input: Sequence<ReferralAggregate>, ctx: AnalysisContext): Sequence<VisitsAnalysis> {
    return input.map( (item) => {
            const memo: VisitsAnalysis = {
                key: item.referral.clientDisplayName,
                latestWorkDate: null,
                totalTimeSpentMins: 0,
                totalVisits: 0,
                countByCommentType: {},
                elements: Lazy([item])
            };

            return item.supportWork.reduce( (prev, work) => accumulateBaseWorkToVisitsAnalysis(prev, work, ctx), memo );
    });
}
*/

/*function activitiesByProject(input: Sequence<ReferralAggregate>): Sequence<DaysOfWeekAnalysis> {
    return groupByReferralProjectName(input).map(pair => {
        const input: Sequence<ReferralAggregate> = Lazy(pair.items);
        return countsOfDayAttending(pair.key, input);
    });
}*/

class SmartStepCountsAccumulator
    implements Accumulator<SmartStepCountsAnalysis, reportDto.ActionDto>
{
    private readonly memo: SmartStepCountsAnalysis;

    constructor(key: string) {
        this.memo = {
            key: key,
            totalAchievedSmartSteps: 0,
            totalOutstandingSmartSteps: 0,
            elements: Lazy([]) // TODO: elementsForGroup.map( (x) => ({referral: x.referral}) )
        };
    }

    private accumulate(prev: SmartStepCountsAnalysis, item: reportDto.ActionDto) {
        prev.totalAchievedSmartSteps += item.achieved;

        // we don't need to 'fix' this outstanding code since this is based on ActionDto which is used by the ReportDemoController
        // which uses the 'latest' smart steps and hence any 'relevant' found is outstanding (whereas getting all the history
        // in ReportDataSourceFactory means that any relevant might not be outstanding since it might be achieved)
        prev.totalOutstandingSmartSteps += item.relevant;

        return prev;
    }

    reduce(actions: Sequence<reportDto.ActionDto>): SmartStepCountsAnalysis {
        return actions.reduce((prev, item) => this.accumulate(prev, item), this.memo);
    }
}

export function smartStepSummaryByKey(
    input: Sequence<reportDto.ActionDto>,
    keyFn: (dto: reportDto.ActionDto) => string
): Sequence<SmartStepCountsAnalysis> {
    return input
        .groupBy(inputElement => keyFn(inputElement))
        .pairs()
        .map(group => {
            const elementsForGroup: Sequence<reportDto.ActionDto> = Lazy(group[1]);
            return new SmartStepCountsAccumulator(group[0]).reduce(elementsForGroup);
        });
}

/** grouped count */
export function countByReferralPropertyReport(
    input: Sequence<ReferralAggregate>,
    keyFn: (dto: ReferralAggregate) => string
): Sequence<{key: string; value: number}> {
    return input
        .groupBy(inputElement => keyFn(inputElement))
        .pairs()
        .map(group => {
            const elementsForGroup: Sequence<ReferralAggregate> = Lazy(group[1]);

            return {key: group[0], value: elementsForGroup.size()};
        });
}

export interface GroupingAnalyser<T> {
    (input: Sequence<T>, ctx?: AnalysisContext): Sequence<Group<T>>;
}

export var keyedResultReferralAnalysers: {[name: string]: GroupingAnalyser<any>} = {
    caseLoadFromSmartStepsByWorker: caseLoadFromSmartStepsByWorker,
    //"visitsBreakdownByReferral": visitsBreakdownByReferral,
    groupByActivityInterest: <any>groupByActivityInterest // HACKed cast
};
