///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import Lazy = require("lazy");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import * as commonTypes from "@eccosolutions/ecco-common";
import * as evidenceDto from "ecco-dto/evidence-dto";
import referralCommonAnalysis = require("./referralCommonAnalysis");
import groupByReferredService = referralCommonAnalysis.groupByReferredService;
import groupByReferralProjectName = referralCommonAnalysis.groupByReferralProjectName;
import groupByReferralAssignedWorker = referralCommonAnalysis.groupByReferralAssignedWorker;
import {SingleValueHistoryDto} from "ecco-dto";
import types = require("./types");
import Accumulator = types.Accumulator;
import Group = types.Group;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import WorkWithRefToReferralAggregate = types.WorkWithRefToReferralAggregate;
import extractPair = types.extractPair;
import GroupFn = types.GroupFn;
import WorkAnalysis = types.WorkAnalysis;
import workCommonAnalysis = require("./workCommonAnalysis");
import {filterWorkBySVHDate, GroupWithWorkWithRef, svhWithWork} from "./workCommonAnalysis";
import {AnalysisContext} from "../chart-domain";
import {columnMap, numberColumn} from "../controls/tableSupport";
import {ensureEntityReferencesParent} from "../tables/predefined-table-representations";
import {GroupedVisitsByWorkTypeAnalysis} from "./workAnalysis";
import {BaseWork, EvidenceDef} from "ecco-dto";
import {
    groupByProjectAllocation,
    groupByServiceAllocation,
    groupBySrId
} from "./referralCommonAnalysis";

export interface CountOfGroup<T> extends Group<T> {
    count?: number;
}



// this was assuming a referral with things, but we are already flattened - we're just work item from WorkAnyAnalysis
//function visitSupportRiskCounts<T extends {supportRiskWork: BaseOutcomeBasedWork[]}>(input: Sequence<T>, groupFn: GroupFn<T>, ctx: AnalysisContext): Sequence<types.GroupWithVisits<T>> {
function visitSupportRiskCounts<T extends BaseWork>(
    input: Sequence<T>,
    groupFn: GroupFn<T>,
    ctx?: AnalysisContext
): Sequence<types.GroupWithVisits<T>> {
    // groupFn just puts the input into a group - no transform etc
    return groupFn(input, ctx).map(pair => {
        // this was assuming a referral with things, but we are already flattened - we're just work item from WorkAnyAnalysis
        //const allWorkInGroup = flattenWithParentByFn(pair.elements, (input: T) => input.supportRiskWork);
        const workWithParent =
            pair.elements.size() > 0
                ? pair.elements.map(e =>
                      ensureEntityReferencesParent(e, pair.elements.first().referralSummary)
                  )
                : pair.elements;
        const memo: types.GroupWithVisits<T> = {
            key: pair.key,
            latestWorkDate: null,
            lastSignedWorkDate: null,
            lastUnSignedWorkDate: null,
            totalTimeSpentMins: 0,
            totalVisits: 0,
            unsignedWorkCount: 0,
            averageVisitLength: 0,
            countByCommentType: {},
            elements: workWithParent
        };
        const result = workWithParent.reduce(
            (prev, work) => workCommonAnalysis.accumulateBaseWorkToVisitsAnalysis(prev, work, ctx),
            memo
        );
        result.averageVisitLength = result.totalTimeSpentMins / result.totalVisits;
        return result;
    });
}

//*********************************
// produce types.ReferralAggregateGroupWithVisits via Group<ReferralAggregate>

function groupByReferralClientName(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.referral.clientDisplayName)
        .pairs()
        .map(extractPair);
}
/**
 * Takes a grouped ReferralAggregate and constructs a types.ReferralAggregateGroupWithVisits on each group.
 * We could have constructed a Group<ReferralAggregate>, but types.ReferralAggregateGroupWithVisits gives us more information out.
 */
function visitSupportCounts(
    input: Sequence<ReferralAggregate>,
    groupFn: GroupFn<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<types.ReferralAggregateGroupWithVisits> {
    return groupFn(input, ctx).map(pair => {
        const allSupportWork = workCommonAnalysis.flattenSupportWork(pair.elements);
        const memo: types.ReferralAggregateGroupWithVisits = {
            key: pair.key,
            latestWorkDate: null,
            lastSignedWorkDate: null,
            lastUnSignedWorkDate: null,
            totalTimeSpentMins: 0,
            totalVisits: 0,
            unsignedWorkCount: 0,
            averageVisitLength: 0,
            countByCommentType: {},
            elements: pair.elements,
            count: 0
        };
        const result = allSupportWork.reduce(
            (prev, work) => workCommonAnalysis.accumulateBaseWorkToVisitsAnalysis(prev, work, ctx),
            memo
        );
        result.averageVisitLength = result.totalTimeSpentMins / result.totalVisits;
        return result;
    });
}
// **
function visitAllCounts(
    input: Sequence<ReferralAggregate>,
    groupFn: GroupFn<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<types.ReferralAggregateGroupWithVisits> {
    return groupFn(input).map(pair => {
        const allWork = workCommonAnalysis.flattenAllWork(pair.elements);
        const memo: types.ReferralAggregateGroupWithVisits = {
            key: pair.key,
            latestWorkDate: null,
            lastSignedWorkDate: null,
            lastUnSignedWorkDate: null,
            totalTimeSpentMins: 0,
            totalVisits: 0,
            unsignedWorkCount: 0,
            averageVisitLength: 0,
            countByCommentType: {},
            elements: pair.elements,
            count: 0
        };
        const result = allWork.reduce(
            (prev, work) => workCommonAnalysis.accumulateBaseWorkToVisitsAnalysis(prev, work, ctx),
            memo
        );
        result.averageVisitLength = result.totalTimeSpentMins / result.totalVisits;
        return result;
    });
}

//*********************************
// produce types.ReferralAggregateGroupWithVisits via Group<WorkWithRefToReferralAggregate>

function groupBySupportEvidenceAuthor(
    input: Sequence<ReferralAggregate>
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    const work = workCommonAnalysis.flattenSupportWork(input);
    return work
        .groupBy(inputElement => inputElement.authorDisplayName || "no author assigned")
        .pairs()
        .map(extractPair);
}
function groupByAllEvidenceAuthor(
    input: Sequence<ReferralAggregate>
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    const work = workCommonAnalysis.flattenAllWork(input);
    return work
        .groupBy(inputElement => inputElement.authorDisplayName || "no author assigned")
        .pairs()
        .map(extractPair);
}

/**
 * Takes ReferralAggregate and runs an intermediate function to flatten to work items, then builds up using
 * VisitsAnalysisFromWorkAccumulator collecting its stats according to the groupFn key used
 */
interface ReferralAggregateToGroupWorkWithRef {
    (input: Sequence<ReferralAggregate>, ctx?: AnalysisContext): Sequence<
        Group<WorkWithRefToReferralAggregate>
    >;
}
function visitCountsFromWork(
    input: Sequence<ReferralAggregate>,
    groupFn: ReferralAggregateToGroupWorkWithRef,
    ctx: AnalysisContext
): Sequence<types.ReferralAggregateGroupWithVisits> {
    return groupFn(input).map(pair => {
        const allWork: Sequence<WorkWithRefToReferralAggregate> = pair.elements;
        return new workCommonAnalysis.VisitsAnalysisFromWorkAccumulator(pair.key, ctx).reduce(
            allWork
        );
    });
}

//*********************************
// Accumulator for ReferralAggregateGroupWithCount

interface ReferralAggregateGroupWithCount extends Group<ReferralAggregate> {
    count: number;
}
interface CountUniqueItems extends ReferralAggregateGroupWithCount {
    uniqueItems: commonTypes.SparseArray<ReferralAggregate>;
}
function accumulateBaseWorkToPropertyCount<T extends ReferralAggregateGroupWithCount>(
    prev: T,
    work: evidenceDto.BaseWork
): T {
    prev.count++;
    return prev;
}
class VisitsAnalysisFromWorkPropertyAccumulator
    implements Accumulator<ReferralAggregateGroupWithCount, WorkWithRefToReferralAggregate>
{
    private memo: CountUniqueItems;
    constructor(key: string) {
        this.memo = {
            key: key,
            count: 0,
            uniqueItems: {},
            elements: Lazy([])
        } as CountUniqueItems;
    }
    private accumulate(
        prev: CountUniqueItems,
        work: WorkWithRefToReferralAggregate
    ): CountUniqueItems {
        prev.uniqueItems[work.reportItem.referral.referralId] = work.reportItem;
        return accumulateBaseWorkToPropertyCount(prev, work);
    }
    private postProcess() {
        this.memo.elements = <Sequence<ReferralAggregate>>Lazy(this.memo.uniqueItems).values();
    }
    reduce(allWork: Sequence<WorkWithRefToReferralAggregate>): ReferralAggregateGroupWithCount {
        const result = allWork.reduce((prev, work) => this.accumulate(prev, work), this.memo);
        this.postProcess();
        return result;
    }
}

//*********************************
// produce ReferralAggregateGroupWithCount via Group<WorkWithRefToReferralAggregate>

function groupBySupportCommentTypeToWork(
    input: Sequence<ReferralAggregate>,
    ctx?: AnalysisContext
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    const work = workCommonAnalysis.flattenSupportWork(input);
    const nameLookup = (id: number) =>
        ctx!.getSessionData().getListDefinitionEntryById(id).getDisplayName();
    return work
        .groupBy(inputElement => nameLookup(inputElement.commentTypeId) || "no type")
        .pairs()
        .map(extractPair);
}
function visitCountsPropertyFromWork(
    input: Sequence<ReferralAggregate>,
    groupFn: ReferralAggregateToGroupWorkWithRef,
    ctx: AnalysisContext
): Sequence<ReferralAggregateGroupWithCount> {
    return groupFn(input, ctx).map(pair => {
        const allWork: Sequence<WorkWithRefToReferralAggregate> = pair.elements;
        return new VisitsAnalysisFromWorkPropertyAccumulator(pair.key).reduce(allWork);
    });
}

export function visitCountsBySrIdAndWorkTypeAnalyser<T extends BaseWork>(
    ctx: AnalysisContext,
    input: Sequence<T>
) {
    return new GroupedVisitsByWorkTypeAnalysis(
        ctx,
        workGroupToVisitGroupByWorkType(groupBySrId(input), ctx)
    );
}
types.analysersByName["visitCountsBySrIdAndWorkTypeAnalyser"] =
    visitCountsBySrIdAndWorkTypeAnalyser;
function workGroupToVisitGroupByWorkType<T extends BaseWork>(
    input: Sequence<Group<T>>,
    ctx: AnalysisContext
): Sequence<types.GroupWithWorkTypeVisits<T>> {
    return input.map(workGroup => {
        const allWork = workGroup.elements;
        // TODO taskEvidenceType may not be ideal - the taskName alone isn't enough
        const supportWorkByWorkType = allWork.filter(w =>
            EvidenceDef.isSupport(EvidenceDef.taskEvidenceType(ctx.getSessionData(), w.taskName))
        );
        const threatWorkByWorkType = allWork.filter(w =>
            EvidenceDef.isRisk(EvidenceDef.taskEvidenceType(ctx.getSessionData(), w.taskName))
        );

        // get visit summary by work type
        const supportVisitsByWorkType: any =
            new workCommonAnalysis.VisitsAnalysisFromWorkAccumulator(
                workGroup.key + " support",
                ctx
            ).reduce(supportWorkByWorkType as any);
        const threatVisitsByWorkType: any =
            new workCommonAnalysis.VisitsAnalysisFromWorkAccumulator(
                workGroup.key + " threat",
                ctx
            ).reduce(threatWorkByWorkType as any);
        const totalVisitsByWorkType: any = new workCommonAnalysis.VisitsAnalysisFromWorkAccumulator(
            workGroup.key + " total",
            ctx
        ).reduce(allWork as any);
        const result: types.GroupWithWorkTypeVisits<T> = {
            key: workGroup.key,

            // TODO see VisitsAnalysisFromWorkAccumulator - should be unique ReferralAggregate
            elements: allWork, // if want to see the elements: WorkWithParentRefAnalysis
            //elements: Lazy(workGroup), // if want to pass to a grouped one: GroupedWorkWithParentRefAnalysis

            count: allWork.size(),
            support: supportVisitsByWorkType,
            threat: threatVisitsByWorkType,
            total: totalVisitsByWorkType
        };
        return result;
    });
}

export function visitSupportRiskCountsBySrIdAnalyser<T extends BaseWork>(
    ctx: AnalysisContext,
    input: Sequence<T>
) {
    return new workCommonAnalysis.GroupedAnalysisWithVisits(
        ctx,
        visitSupportRiskCounts(input, referralCommonAnalysis.groupBySrId, ctx)
    );
}
types.analysersByName["visitSupportRiskCountsBySrIdAnalyser"] =
    visitSupportRiskCountsBySrIdAnalyser;

//export function visitSupportRiskCountsByServiceAnalyser<T extends {serviceAllocationId: number, supportRiskWork: BaseOutcomeBasedWork[]}>(ctx: AnalysisContext, input: Sequence<T>) {
export function visitSupportRiskCountsByServiceAnalyser<T extends BaseWork>(ctx: AnalysisContext, input: Sequence<T>) {
    return new workCommonAnalysis.GroupedAnalysisWithVisits(ctx, visitSupportRiskCounts(input, groupByServiceAllocation, ctx));
}
types.analysersByName['visitSupportRiskCountsByServiceAnalyser'] = visitSupportRiskCountsByServiceAnalyser;

export function visitSupportRiskCountsByProjectAnalyser<T extends BaseWork>(
    ctx: AnalysisContext,
    input: Sequence<T>
) {
    return new workCommonAnalysis.GroupedAnalysisWithVisits(
        ctx,
        visitSupportRiskCounts(input, groupByProjectAllocation, ctx)
    );
}
types.analysersByName["visitSupportRiskCountsByProjectAnalyser"] =
    visitSupportRiskCountsByProjectAnalyser;

export var visitSupportCountsByClientAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitSupportCounts(input, groupByReferralClientName, ctx)
    );
};
export var visitSupportCountsByServiceAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitSupportCounts(input, groupByReferredService, ctx)
    );
};
export var visitSupportCountsByProjectAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitSupportCounts(input, groupByReferralProjectName, ctx)
    );
};
export var visitSupportCountsByWorkerAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitSupportCounts(input, groupByReferralAssignedWorker, ctx)
    );
};
export var visitSupportCountsByAuthorAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitCountsFromWork(input, groupBySupportEvidenceAuthor, ctx)
    );
};

//*********************************
// SingleValueHistoryWithRefToReferralAggregate

export var visitSupportCountsBySupportLevelAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitCountsFromSVH(input, groupWorkBySVHValue, ctx)
    );
};

function visitCountsFromSVH(
    input: Sequence<ReferralAggregate>,
    groupFn: GroupFn<WorkWithRefToReferralAggregate>,
    ctx: AnalysisContext
): Sequence<types.ReferralAggregateGroupWithVisits> {
    const allWork = workCommonAnalysis.flattenSupportWork(input);
    return groupFn(allWork, ctx).map(groupWork => {
        const work: Sequence<WorkWithRefToReferralAggregate> = groupWork.elements;
        return new workCommonAnalysis.VisitsAnalysisFromWorkAccumulator(groupWork.key, ctx).reduce(
            work
        );
    });
}

function groupWorkBySVHValue(
    input: Sequence<WorkWithRefToReferralAggregate>,
    ctx: AnalysisContext
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    return convertToGroupBySVHValueWithWork(
        groupBySVHValue(filterWorkBySVHDate(svhWithWork(input)))
    );
}

function convertToGroupBySVHValueWithWork(
    input: Sequence<Group<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>>
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    //const sessionData = ctx.getSessionData();
    //const qnId = from supportHoursQn from report defn
    //key: sessionData!.getAnswerDisplayValue(qnId, input.key),

    return input.map(input => {
        return {
            key: input.key,
            elements: input.elements
                .map(svhWithWorkRA => svhWithWorkRA.work)
                .flatten<WorkWithRefToReferralAggregate>()
        };
    });
}

function groupBySVHValue(
    input: Sequence<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>
): Sequence<Group<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>> {
    return (
        input
            .groupBy(svhWithWorkRA =>
                // the 'key' is the id of the value (the list def id) with the date applicable
                // this is so that we group and filter support on key:date combinations - not just key,
                // which might otherwise exclude support work because the 'first' key date found is not applicable
                svhWithWorkRA.svh.value ? svhWithWorkRA.svh.value.toString() : "no value"
            )
            // so far this will be the 'value' with an array of {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}
            .pairs()
            // pairs moves this structure into an array of ['value', {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}[]]
            .map(types.extractPair)
        // turns the pairs array into a Group {key: array[0], elements: array[1]}
        // which is key: 'value', elements: {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}
    );
}

//*********************************

export var visitAllCountsByServiceAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitAllCounts(input, groupByReferredService, ctx)
    );
};
export var visitAllCountsByProjectAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitAllCounts(input, groupByReferralProjectName, ctx)
    );
};
export var visitAllCountsByAssignedWorkerAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitAllCounts(input, groupByReferralAssignedWorker, ctx)
    );
};
export var visitAllCountsByAuthorAnalyser: Transformer<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
    return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(
        ctx,
        visitCountsFromWork(input, groupByAllEvidenceAuthor, ctx)
    );
};

//*********************************
// Analyers for ReferralAggregateGroupWithCount

const visitsCountColumns = columnMap(
    numberColumn<ReferralAggregateGroupWithCount>("count", row => row.count)
);

class GroupedReferralAggregateCountAnalysis extends SequenceAnalysis<ReferralAggregateGroupWithCount> {
    constructor(ctx: AnalysisContext, data: Sequence<ReferralAggregateGroupWithCount>) {
        super(ctx, data, (item: Group<ReferralAggregate>) => item.key);
        this.recordRepresentation = {
            VisitsAnalysis: visitsCountColumns
        };
    }
}
export var visitSupportCountsByCommentTypeAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithCount
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithCount> {
    return new GroupedReferralAggregateCountAnalysis(
        ctx,
        visitCountsPropertyFromWork(input, groupBySupportCommentTypeToWork, ctx)
    );
};
