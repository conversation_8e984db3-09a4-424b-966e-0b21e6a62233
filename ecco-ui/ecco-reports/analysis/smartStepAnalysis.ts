///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import {EccoDateTime, StringUtils} from "@eccosolutions/ecco-common";
import * as evidenceDto from "ecco-dto";
import {
    BaseOutcomeBasedWork,
    BaseActionInstance,
    SupportAction,
    BaseActionInstanceSnapshotDto,
    FlagEvidenceDto,
    SupportWork,
    SmartStepStatusName
} from "ecco-dto";
import {RiskActionEvidenceDto, RiskGroupEvidenceDto, RiskWorkEvidenceDto} from "ecco-dto";
import {ReferralSummaryDto} from "ecco-dto";
import {AnalysisContext} from "../chart-domain";
import {
    flattenWithParentByFn,
    referralAggregateReportItemColumns,
    referralSummaryColumns,
    workerColumns
} from "../tables/predefined-table-representations";
import {EntityWithParent, WorkWithRefToReferralAggregate} from "./types";
import {baseOutcomeWorkColumns, smartStepOnlyColumns} from "./workCommonAnalysis";
import {StaffDto} from "ecco-dto";
import {
    booleanColumn,
    columnMap,
    dateTimeColumn,
    fixedPrecisionNumberColumn,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import tableRepresentations = require("../tables/predefined-table-representations");
import types = require("./types");
import smartStepCount = require("./smartStepCount");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Analyser = types.Analyser;
import Group = types.Group;
import GroupedAnalysis = types.GroupedAnalysis;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import SupportActionWithRefToReferralAggregate = types.SupportActionWithRefToReferralAggregate;
import extractPair = types.extractPair;
import GroupFn = types.GroupFn;
import {expandByScheduleTime} from "ecco-rota";

interface PrePostChangeEntry<T extends {id: number | null}> {
    pre: T;
    post: T | null;
}

interface SmartStepInstanceWithParent extends SupportAction {
    parent: BaseActionInstanceSnapshotDto;
}
export interface SmartStepSnapshotDifference
    extends PrePostChangeEntry<SmartStepInstanceWithParent> {
    difference: string;
}

/**
 * @param groupedSnapshots
 * @param strict means all data points are required when true, or just provide the one data point if false
 */
function transformOrderedSnapshotsToPrePost<T extends {id: number | null}>(
    groupedSnapshots: Sequence<Group<T>>,
    strict = true
): Sequence<PrePostChangeEntry<T>> {
    const data = groupedSnapshots
            .map(snapshot => {
                // ASSUME order is pre -> during1 -> during2 (which it is - see  the load order in reportDataSourceFactory)
                let allOrderByPeriod: T[] = snapshot.elements.toArray();

                // ordered by period means we get the 'from' elements first
                // so here we have the same criteria, with the elements in date order
                // which means we can calculate the pre and post values for the
                // answers given (of which there can only be a max of 3 answers)
                // we try to load 3 data points, but we may have zero, one, two or three smart step points loaded
                if (allOrderByPeriod.length >= 2) {
                    // 3 snapshot answers means we take the first and last
                    // NB the second and third data point can be the exact same data point
                    // as they satisfy both the earliest and latest within the range
                    if (allOrderByPeriod.length == 3) {
                        let cell: PrePostChangeEntry<T> = {
                            pre: allOrderByPeriod[0], // this will be PRE
                            post: allOrderByPeriod[2] // this will be DURING2
                        };
                        return cell;
                        // otherwise we take the order given
                    } else if (allOrderByPeriod.length == 2) {
                        let cell: PrePostChangeEntry<T> = {
                            pre: allOrderByPeriod[0], // this could be PRE or DURING1
                            post: allOrderByPeriod[1] // this could be DURING1 or DURING2
                        };
                        // if they are the exact same data item - which can happen
                        // if they satisfy both the earliest and latest within the range
                        // then we obey the strict setting and let one through
                        if (cell.pre.id == cell.post!.id) {
                            if (strict) {
                                return null;
                            } else {
                                // allow the pre to show, but blank the post - this makes it clear there is one data point
                                // TODO comment the below to not override with null - simply show no change
                                cell.post = null;
                            }
                        }
                        return cell;
                    }
                }
                // if we have a data point, show it even if not pre-post
                if (!strict && allOrderByPeriod.length == 1) {
                    let cell: PrePostChangeEntry<T> = {
                        pre: allOrderByPeriod[0], // this will be PRE
                        post: null
                    };
                    return cell;
                }

                // there is a chance we don't have 2 data points (or 1), so there is no pre-post
                return null;
            });
            // filter out those that don't have data points
            //.filter(entry => entry != null);
    // we filter out the above null specifically here, to show the typing anomaly
    const dataNoNull = data.filter(entry => entry != null)
    return dataNoNull as Sequence<PrePostChangeEntry<T>>;
}

//*********************************
// Analysis: SmartStepsSnapshotOnlyAnalysis
// useful really only as debugging but kicks off the flattening, by actionDef and matrix analyser
// data is as-given - a SnapshotPeriod ordered collection of the pre/during1/during2
// but each dto may be split across another dto due to paging, so each dto is not unique by serviceRecipientId
export class SmartStepsSnapshotOnlyAnalysis extends SequenceAnalysis<BaseActionInstanceSnapshotDto> {
    constructor(ctx: AnalysisContext, data: Sequence<BaseActionInstanceSnapshotDto>) {
        super(ctx, data, () => ""); // keyFn null since this analyser not intended to be used in Group
        this.derivativeAnalysers = {
            // do more useful things with the data by flattening with the answers
            smartStepsSnapshotFlatten: smartStepsSnapshotFlattenAnalyser,
            // allow us to filter out the configs - just a client side thing as legacy data
            filterActionsWithMetaData: filterActionsWithMetaDataAnalyser
        };
        this.recordRepresentation = {
            // if debugging, a table representation may be useful
        };
    }
}

/**
 * Horrendous use of finding questionsToActions across all referralaspects(!) to filter actions
 * But it does whats needed - its basically a way of saying 'use this analyser to do modern config only'
 * in particular circumstances.
 * The better approach may be to specify a taskName to filter to a newer page, not just specifying the evidenceGroup.
 */
let filterActionsWithMetaDataAnalyser: Transformer<
    BaseActionInstanceSnapshotDto,
    BaseActionInstanceSnapshotDto
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseActionInstanceSnapshotDto>
): SequenceAnalysis<BaseActionInstanceSnapshotDto> {
    return new SmartStepsSnapshotOnlyAnalysis(ctx, filterActionsWithMetaData(ctx, input));
};
const actionDefIdsFromGlobalMetaData = (ctx: AnalysisContext): number[] => {
    return ctx
        .getSessionData()
        .getTaskDefinitions()
        .filter(td => td.metadata && td.metadata.questionsToActions)
        .map(td => td.metadata.questionsToActions!.map(d => d[1]))
        .reduce((r, x) => r.concat(x), []); // flatMap
};
function filterActionsWithMetaData(
    ctx: AnalysisContext,
    input: Sequence<BaseActionInstanceSnapshotDto>
): Sequence<BaseActionInstanceSnapshotDto> {
    const actionsToFilter = actionDefIdsFromGlobalMetaData(ctx);
    return input
        .filter(
            snapshot =>
                snapshot != null &&
                snapshot.latestActions != null &&
                snapshot.latestActions.length > 0
        )
        .filter(snapshot =>
            snapshot.latestActions.some(a => actionsToFilter.indexOf(a.actionId) > -1)
        );
}

let smartStepsSnapshotFlattenAnalyser: Transformer<
    BaseActionInstanceSnapshotDto,
    SmartStepInstanceWithParent
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseActionInstanceSnapshotDto>
): SequenceAnalysis<SmartStepInstanceWithParent> {
    return new SmartStepsSnapshotWithParentAnalysis(ctx, flattenSmartStepsSnapshot(input));
};
export function flattenSmartStepsSnapshot(
    input: Sequence<BaseActionInstanceSnapshotDto>
): Sequence<SmartStepInstanceWithParent> {
    return input
        .filter(
            snapshot =>
                snapshot != null &&
                snapshot.latestActions != null &&
                snapshot.latestActions.length > 0
        )
        .map(snapshot =>
            snapshot.latestActions.map(answer =>
                ensureSmartStepInstanceReferencesParent(snapshot, answer)
            )
        )
        .flatten<SmartStepInstanceWithParent>();
}
function ensureSmartStepInstanceReferencesParent(
    parent: BaseActionInstanceSnapshotDto,
    action: BaseActionInstance
): SmartStepInstanceWithParent {
    let result = <SmartStepInstanceWithParent>action;
    result.parent = parent;
    return result;
}

//*********************************
// Analysis: SmartStepsSnapshotWithParentAnalysis
// flattened SmartStepInstanceWithParent to then perform analysis
// the analysis we can do here is to create a a pre/post per action instance to compare
class SmartStepsSnapshotWithParentAnalysis extends SequenceAnalysis<SmartStepInstanceWithParent> {
    constructor(ctx: AnalysisContext, data: Sequence<SmartStepInstanceWithParent>) {
        super(ctx, data, () => ""); // keyFn null since this analyser not intended to be used in Group
        this.derivativeAnalysers = {
            smartStepsSnapshotDifferenceByActionInstance: smartStepsSnapshotDifferenceAnalyser,
            smartStepsSnapshotByTargetDate: smartStepsSnapshotByTargetDateAnalyser,
            smartStepsSnapshotGroupByDue: smartStepsSnapshotGroupByDueAnalyser
        };
        this.recordRepresentation = {
            //"supportActionWithParent": supportActionWithParentColumns
            //"SmartStepOnly": smartStepOnlyColumns,
            SmartStepSingleSnapshotColumns: smartStepSingleSnapshotColumns
        };
    }
}

function getQuantisedDue(value: EccoDateTime | null): string {
    if (!value) {
        return "not due";
    }
    if (value.earlierThan(EccoDateTime.nowLocalTime())) {
        return "overdue";
    } else {
        return "due";
    }
}
function countsBy<T>(input: Sequence<T>, extractKey: (input: T) => string): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => extractKey(inputElement))
        .pairs()
        .map(extractPair)
        .map(pair => {
            return {
                key: pair.key,
                count: pair.elements.size(),
                elements: pair.elements
            };
        });
}
let smartStepsSnapshotGroupByDueAnalyser: Transformer<
    SmartStepInstanceWithParent,
    Group<SmartStepInstanceWithParent>
> = function (
    ctx: AnalysisContext,
    input: Sequence<SmartStepInstanceWithParent>
): SequenceAnalysis<Group<SmartStepInstanceWithParent>> {
    return new GroupedSmartStepsSnapshotWithParentAnalysis(
        ctx,
        countsBy(input, s => getQuantisedDue(EccoDateTime.parseIso8601(s.targetDateTime)))
    );
};
class GroupedSmartStepsSnapshotWithParentAnalysis extends GroupedAnalysis<
    SmartStepInstanceWithParent,
    Group<SmartStepInstanceWithParent>
> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<SmartStepInstanceWithParent>>) {
        super(ctx, data);
        //this.recordRepresentation = {
        //    "GroupTaskStatusColumns": groupTaskStatusColumns
        //};
        // this.derivativeAnalysers = {
        //     "unGroupRelationship": unGroupWorkWithRefGroupWithCountAnalyser // WIP
        // };
        this.addOnClickAnalyser("ungroup", wrapSmartStepSnapshotAnalyser);
        this.addOnClickManyAnalysis("ungroup", SmartStepsSnapshotWithParentAnalysis);
    }
}
// let groupTaskStatusColumns = columnMap(
//     // choose one of these 'key' columns according to the analyser before this chart/table
//     textColumn<Group<TaskStatus>>("key", (row) => row.key),
//     numberColumn<Group<TaskStatus>>("count", (row) => row.count)
// );
let wrapSmartStepSnapshotAnalyser: Analyser<
    Group<SmartStepInstanceWithParent>,
    Sequence<SmartStepInstanceWithParent>
> = function (
    ctx: AnalysisContext,
    input: Group<SmartStepInstanceWithParent>
): SmartStepsSnapshotWithParentAnalysis {
    return new SmartStepsSnapshotWithParentAnalysis(ctx, input.elements);
};
/**
 * A difference report shows changes over actionInstanceUuids
 */
let smartStepsSnapshotDifferenceAnalyser: Transformer<
    SmartStepInstanceWithParent,
    SmartStepSnapshotDifference
> = function (
    ctx: AnalysisContext,
    input: Sequence<SmartStepInstanceWithParent>
): SequenceAnalysis<SmartStepSnapshotDifference> {
    // get all the raw answers by action instance
    const groupByActionInstance: Sequence<Group<SmartStepInstanceWithParent>> =
        types.groupByProperty(input, "actionInstanceUuid", ctx);

    // each actionInstance could exist 1, 2 or 3 times depending on the number of snapshot values
    // so we transform them into the correct pre/post differences
    // include ones that might only have one entry
    const prePosts: Sequence<PrePostChangeEntry<SmartStepInstanceWithParent>> =
        transformOrderedSnapshotsToPrePost(groupByActionInstance, false);

    // calculate the difference for each sequence item
    // which is for each srId and questionId
    const differences: Sequence<SmartStepSnapshotDifference> = prePosts.map(prePost => {
        const diff = <SmartStepSnapshotDifference>prePost;
        const statusPre = prePost.pre && prePost.pre.status;
        const statusPost = prePost.post && prePost.post.status;
        diff.difference = statusPost
            ? SmartStepStatusName[statusPost]
            : statusPre
            ? SmartStepStatusName[statusPre]
            : "unknown";
        return diff;
    });

    return new SmartStepSnapshotDifferenceAnalysis(ctx, differences);
};

/**
 * A sort over targetDate - because the server side delivers data per client
 */
let smartStepsSnapshotByTargetDateAnalyser: Transformer<
    SmartStepInstanceWithParent,
    SmartStepInstanceWithParent
> = function (
    ctx: AnalysisContext,
    input: Sequence<SmartStepInstanceWithParent>
): SequenceAnalysis<SmartStepInstanceWithParent> {
    // dashboard 'checks due' uses this, and data is server side, so we expand from the targetDateTime
    const actionsPerTime = expandByScheduleTime(input.toArray());

    return new SmartStepsSnapshotWithParentAnalysis(ctx, Lazy(actionsPerTime));
};

//*********************************
// Analysis: SmartStepSnapshotDifferenceAnalysis
// simply spits out the results
export class SmartStepSnapshotDifferenceAnalysis extends SequenceAnalysis<SmartStepSnapshotDifference> {
    constructor(ctx: AnalysisContext, data: Sequence<SmartStepSnapshotDifference>) {
        super(ctx, data, () => ""); // TODO
        this.derivativeAnalysers = {
            //"flattenWithSnapshotData": toQuestionAnswerSnapshotWithParentFromDifferenceAnalyser
        };
        this.recordRepresentation = {
            differenceColumns: smartStepMultiSnapshotOnlyColumns,
            // TODO if we detect fetchEntities 'referral' we should be able to pick up the columns
            differenceColumnsWithReferral: smartStepMultiSnapshotColumns
        };
    }
}

// a pre value must exist, so put some top-level columns to look better than 'pre-a: a-id'
const smartStepMultiSnapshotOnlyColumns = columnMap(
    numberColumn<SmartStepSnapshotDifference>("sr-id", row => row.pre.parent.serviceRecipientId),
    textColumn<SmartStepSnapshotDifference>("result", row => row.difference)
);
const smartStepMultiSnapshotToSmartStepOnly = joinNestedPathColumnMaps<
    SmartStepSnapshotDifference,
    SupportAction
>("a", row => row.pre, smartStepOnlyColumns);
const smartStepMultiSnapshotToReferralColumns = joinNestedPathColumnMaps<
    SmartStepSnapshotDifference,
    ReferralSummaryDto
>("r", row => row.pre.parent.referralSummary, referralSummaryColumns);
const smartStepMultiSnapshotToSmartStepOnlyPre = joinNestedPathColumnMaps<
    SmartStepSnapshotDifference,
    SupportAction
>("pre-a", row => row.pre, smartStepOnlyColumns);
const smartStepMultiSnapshotToSmartStepOnlyPost = joinNestedPathColumnMaps<
    SmartStepSnapshotDifference,
    SupportAction
>("post-a", row => row.post, smartStepOnlyColumns);
const smartStepMultiSnapshotColumns = joinColumnMaps(
    smartStepMultiSnapshotOnlyColumns,
    smartStepMultiSnapshotToReferralColumns,
    smartStepMultiSnapshotToSmartStepOnly,
    smartStepMultiSnapshotToSmartStepOnlyPre,
    smartStepMultiSnapshotToSmartStepOnlyPost
);

const smartStepSingleSnapshotToReferralColumns = joinNestedPathColumnMaps<
    SmartStepInstanceWithParent,
    ReferralSummaryDto
>("r", row => row.parent.referralSummary, referralSummaryColumns);
const smartStepSingleSnapshotToWorkerColumns = joinNestedPathColumnMaps<
    SmartStepInstanceWithParent,
    StaffDto
>("w", row => row.parent.staff, workerColumns);
const smartStepSingleSnapshotColumns = joinColumnMaps(
    smartStepOnlyColumns,
    smartStepSingleSnapshotToReferralColumns,
    smartStepSingleSnapshotToWorkerColumns
);

//*********************************
// EXPORT group analyser
// SupportActionWithRefToReferralAggregate to SupportActionWithRefToReferralAggregate_Group

const smartStepWithRefCountColumns = columnMap(
    textColumn<SupportActionWithRefToReferralAggregate_Group>("key", row => row.key),
    numberColumn<SupportActionWithRefToReferralAggregate_Group>("count", row => row.count),
    numberColumn<SupportActionWithRefToReferralAggregate_Group>(
        "achieved",
        row => row.totalAchievedSmartSteps
    ),
    numberColumn<SupportActionWithRefToReferralAggregate_Group>(
        "outstanding",
        row => row.totalOutstandingSmartSteps
    ),
    fixedPrecisionNumberColumn<SupportActionWithRefToReferralAggregate_Group>(
        "success %",
        0,
        row => row.percentSuccess
    )
);

/** result */
interface SupportActionWithRefToReferralAggregate_Group
    extends Group<SupportActionWithRefToReferralAggregate> {
    // since the incoming SupportActionWithRefToReferralAggregate is broken down into
    // a row per SupportAction
    count: number;
    totalAchievedSmartSteps: number;
    totalOutstandingSmartSteps: number;
    percentSuccess: number;
}

/** ungroup on click */
const wrapSmartStepSequenceWithOriginalAnalyser: Analyser<
    Group<SupportActionWithRefToReferralAggregate>,
    Sequence<SupportActionWithRefToReferralAggregate>
> = function (
    ctx: AnalysisContext,
    input: Group<SupportActionWithRefToReferralAggregate>
): SupportAction_withRefTo_ReferralAggregate_Analysis {
    return new SupportAction_withRefTo_ReferralAggregate_Analysis(ctx, input.elements);
};

/** Stage Analysis */
class SmartStep_withRefTo_ReferralAggregate_GroupedAnalysis extends GroupedAnalysis<
    SupportActionWithRefToReferralAggregate,
    SupportActionWithRefToReferralAggregate_Group
> {
    constructor(
        ctx: AnalysisContext,
        data: Sequence<SupportActionWithRefToReferralAggregate_Group>
    ) {
        super(ctx, data);
        this.recordRepresentation = {
            SmartStepWithRefCounts: smartStepWithRefCountColumns
        };
        this.addOnClickAnalyser("ungroup", wrapSmartStepSequenceWithOriginalAnalyser);
        this.addOnClickManyAnalysis("ungroup", SupportAction_withRefTo_ReferralAggregate_Analysis);
    }
}

/** Builder */
function buildSmartStepGroupData(
    input: Sequence<SupportActionWithRefToReferralAggregate>,
    groupFn: GroupFn<SupportActionWithRefToReferralAggregate>
): Sequence<SupportActionWithRefToReferralAggregate_Group> {
    return groupFn(input).map(pair => {
        let elements: Sequence<SupportActionWithRefToReferralAggregate> = pair.elements;
        let smartStepsCount = new smartStepCount.SmartStepCount(
            elements
                .map(supportActionWithRef => {
                    let result: types.SupportActionWithWork = {
                        supportWork: supportActionWithRef.supportWork,
                        supportAction: supportActionWithRef
                    };
                    return result;
                })
                .flatten<types.SupportActionWithWork>()
        );
        let result: SupportActionWithRefToReferralAggregate_Group = {
            key: pair.key,
            elements: pair.elements,
            // NB a count of smart steps
            count: pair.elements.flatten().size(),
            totalAchievedSmartSteps: smartStepsCount.getTotalAchievedSmartSteps(),
            totalOutstandingSmartSteps: smartStepsCount.getTotalOutstandingSmartSteps(),
            percentSuccess: smartStepsCount.getPercentSuccess()
        };
        return result;
    });
}

function groupByWorker(
    input: Sequence<SupportActionWithRefToReferralAggregate>
): Sequence<Group<SupportActionWithRefToReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.reportItem.referral.supportWorkerDisplayName)
        .pairs()
        .map(extractPair);
}
export var smartStepWithRefCountsByWorkerAnalyser: Transformer<
    SupportActionWithRefToReferralAggregate,
    SupportActionWithRefToReferralAggregate_Group
> = function (
    ctx: AnalysisContext,
    input: Sequence<SupportActionWithRefToReferralAggregate>
): SequenceAnalysis<SupportActionWithRefToReferralAggregate_Group> {
    return new SmartStep_withRefTo_ReferralAggregate_GroupedAnalysis(
        ctx,
        buildSmartStepGroupData(input, groupByWorker)
    );
};

// BELOW IS ACTUALLY smartStep data structure not actionDef - see buildSupportActionWithRefToReferralAggregate
// we extract the ActionDef but the number of rows directly relates to the number of supportActions

//*********************************
// EXPORT analyser
// ReferralAggregate to SupportActionWithRefToReferralAggregate

const smartStepWithRefColumns = columnMap(
    textColumn<SupportActionWithRefToReferralAggregate>(
        "r-id",
        row => row.reportItem.referral.referralCode || row.reportItem.referral.referralId.toString()
    ),
    numberColumn<SupportActionWithRefToReferralAggregate>(
        "sr-id",
        row => row.supportWork.serviceRecipientId
    ),
    textColumn<SupportActionWithRefToReferralAggregate>("w-id", row => row.supportWork.id),
    textColumn<SupportActionWithRefToReferralAggregate>(
        "client",
        row => row.reportItem.referral.clientDisplayName
    ),
    dateTimeColumn<SupportActionWithRefToReferralAggregate>("work date", row =>
        EccoDateTime.parseIso8601(row.supportWork.workDate)
    ),
    textColumn<SupportActionWithRefToReferralAggregate>("type", (row, ctx) =>
        ctx
            .getSessionData()
            .getListDefinitionEntryById(row.supportWork.commentTypeId)
            .getDisplayName()
    ),
    numberColumn<SupportActionWithRefToReferralAggregate>(
        "time (mins)",
        row => row.supportWork.minsSpent
    ),
    textColumn<SupportActionWithRefToReferralAggregate>("task", row =>
        tableRepresentations.evidenceTaskNameLookup(
            row.supportWork.taskName,
            row.supportWork.serviceAllocationId
        )
    )
);

export const smartStepWithRefAnalysisColumns = joinColumnMaps(
    smartStepOnlyColumns,
    smartStepWithRefColumns
);

export class SmartStepOnlyAnalysis extends SequenceAnalysis<evidenceDto.SupportAction> {
    constructor(ctx: AnalysisContext, data: Sequence<evidenceDto.SupportAction>) {
        super(ctx, data, (item: evidenceDto.SupportAction) => item.id!.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            SmartStepOnly: smartStepOnlyColumns
        };
    }
}

export const flattenToSmartStepsAnalyser: Transformer<
    evidenceDto.SupportWork,
    evidenceDto.SupportAction
> = function (
    ctx: AnalysisContext,
    input: Sequence<evidenceDto.SupportWork>
): SequenceAnalysis<evidenceDto.SupportAction> {
    return new SmartStepOnlyAnalysis(ctx, flattenToSmartSteps(input));
};
types.analysersByName["flattenToSmartStepsAnalyser"] = flattenToSmartStepsAnalyser;

export function flattenToSmartSteps(
    input: Sequence<evidenceDto.SupportWork>
): Sequence<evidenceDto.SupportAction> {
    // allActions
    return input
        .filter(work => work != null)
        .map(work => work.actions)
        .flatten<evidenceDto.SupportAction>();
}

/**
 * Stage Analysis class which shows what we can do with an SupportActionWithRefToReferralAggregate
 */
class SupportAction_withRefTo_ReferralAggregate_Analysis extends SequenceAnalysis<SupportActionWithRefToReferralAggregate> {
    constructor(ctx: AnalysisContext, data: Sequence<SupportActionWithRefToReferralAggregate>) {
        super(ctx, data, (item: SupportActionWithRefToReferralAggregate) => item.id!.toString());
        this.derivativeAnalysers = {
            smartStepWithRefCountsByWorker: smartStepWithRefCountsByWorkerAnalyser
        };
        this.recordRepresentation = {
            SmartStepWithRef: smartStepWithRefAnalysisColumns
        };
        //this.addOnClickAnalyser("ungroup", wrapWorkWithParentRefSequenceAnalyser);
    }
}

/**
 * Builder
 * also see smartStepCountsAnalysis.smartStepCountsReport
 */
function buildSupportActionWithRefToReferralAggregate(
    input: Sequence<ReferralAggregate>
): Sequence<SupportActionWithRefToReferralAggregate> {
    // allActionDefs
    return input
        .filter(ra => ra != null)
        .filter(ra => ra.supportWork != null)
        .map(ra =>
            ra.supportWork!
                .filter(supportWork => supportWork != null)
                .map(supportWork =>
                    supportWork.actions
                        .filter(supportAction => supportAction != null)
                        .map(supportAction => {
                            let result = <SupportActionWithRefToReferralAggregate>supportAction;
                            result.reportItem = ra;
                            result.supportWork = supportWork;
                            return result;

                            // to populate the actionDef we could do this... but we get the name from the SupportAction
                            // var actionDef = lookupActionDef(actionDefId);
                            // var result = <SupportActionWithRefToReferralAggregate>actionDef;
                            // result.reportItem = ra;
                            // return result;

                            //return {reportItem: ra, supportWork: supportWork, supportAction: supportAction,
                            //    id: supportAction.actionId, name: supportAction.name, activityTypes: [], orderby: 0};
                        })
                )
        )
        .flatten<SupportActionWithRefToReferralAggregate>();
}

/**
 * Analyser to transform an incoming data structure (ReferralAggregate) to another (SupportActionWithRefToReferralAggregate)
 */
export const supportAction_withRefTo_ReferralAggregate_Analyser: Transformer<
    ReferralAggregate,
    SupportActionWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<SupportActionWithRefToReferralAggregate> {
    return new SupportAction_withRefTo_ReferralAggregate_Analysis(
        ctx,
        buildSupportActionWithRefToReferralAggregate(input)
    );
};


let associatedActionDefGroupByActionDefAnalyser: Transformer<
    AssociatedActionWithWork,
    Group<AssociatedActionWithWork>
> = function (
    ctx: AnalysisContext,
    input: Sequence<AssociatedActionWithWork>
): SequenceAnalysis<Group<AssociatedActionWithWork>> {
    return new GroupedAssociatedActionWithWorkAnalysis(
        ctx,
        countsBy(input, a =>
            StringUtils.trimText(
                ctx.getSessionData().getAnyActionById(a.associatedActionId).getName(),
                25
            )
        )
    );
};
export class GroupedAssociatedActionWithWorkAnalysis extends SequenceAnalysis<
    Group<AssociatedActionWithWork>
> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<AssociatedActionWithWork>>) {
        super(ctx, data, (item: Group<AssociatedActionWithWork>) => item.key);
        this.derivativeAnalysers = {
            //
        };
        this.recordRepresentation = {
            //
        };
        this.addOnClickAnalyser("ungroup", WrapUnGroupAssociatedActionWithWorkCountAnalyser);
        this.addOnClickManyAnalysis("ungroup", AssociatedActionWithWorkAnalysis);
    }
}
const WrapUnGroupAssociatedActionWithWorkCountAnalyser: Analyser<
    Group<AssociatedActionWithWork>,
    Sequence<AssociatedActionWithWork>
> = function (
    ctx: AnalysisContext,
    input: Group<AssociatedActionWithWork>
): AssociatedActionWithWorkAnalysis {
    return new AssociatedActionWithWorkAnalysis(ctx, input.elements);
};

// SUPPORT WORK - BASED
export interface AssociatedActionWithWork extends evidenceDto.BaseWork {
    associatedActionId: number;
}
export const flattenToAssociatedActionsWithWorkAnalyser: Transformer<
    BaseOutcomeBasedWork,
    AssociatedActionWithWork
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseOutcomeBasedWork>
): SequenceAnalysis<AssociatedActionWithWork> {
    return new AssociatedActionWithWorkAnalysis(ctx, flattenToAssociatedActionsWithWork(input));
};
types.analysersByName["flattenToAssociatedActionsWithWorkAnalyser"] =
    flattenToAssociatedActionsWithWorkAnalyser;
export class AssociatedActionWithWorkAnalysis extends SequenceAnalysis<AssociatedActionWithWork> {
    constructor(ctx: AnalysisContext, data: Sequence<AssociatedActionWithWork>) {
        super(ctx, data, (item: AssociatedActionWithWork) => item.id.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            AssociatedActionWithWork: associatedActionWithWorkColumns,
            AssociatedActionWithWorkWithReferral:
                associatedActionWithWorkWithReferralAggregateColumns, // from referral query
            AssociatedActionWithWorkWithReferralSummary:
                associatedActionWithWorkWithReferralSummaryColumns // from support query
        };
    }
}
const associatedActionColumns = columnMap(
    numberColumn<AssociatedActionWithWork>("aa-id", row => row.associatedActionId),
    textColumn<AssociatedActionWithWork>("aa-name", (row, ctx) => {
        return ctx.getSessionData().getAnyActionById(row.associatedActionId).getName();
    }),
    textColumn<AssociatedActionWithWork>("o-name", (row, ctx) => {
        return ctx.getSessionData().getAnyActionById(row.associatedActionId).getOutcome().getName();
    })
);
export const associatedActionWithWorkColumns = joinColumnMaps(
    baseOutcomeWorkColumns,
    associatedActionColumns
);
const associatedActionToReferralAggregateColumns = joinNestedPathColumnMaps<
    AssociatedActionWithWork,
    ReferralAggregate
>(
    "r",
    row => (<WorkWithRefToReferralAggregate>(<any>row)).reportItem,
    referralAggregateReportItemColumns
);
export const associatedActionWithWorkWithReferralAggregateColumns = joinColumnMaps(
    associatedActionWithWorkColumns,
    associatedActionToReferralAggregateColumns
);
const associatedActionToReferralSummaryOnlyColumns = joinNestedPathColumnMaps<
    AssociatedActionWithWork,
    ReferralSummaryDto
>("r", row => (<SupportWork>(<any>row)).referralSummary, referralSummaryColumns);
export const associatedActionWithWorkWithReferralSummaryColumns = joinColumnMaps(
    associatedActionWithWorkColumns,
    associatedActionToReferralSummaryOnlyColumns
);

function flattenToAssociatedActionsWithWork(
    input: Sequence<BaseOutcomeBasedWork>
): Sequence<AssociatedActionWithWork> {
    return input
        .filter(wk => wk != null && wk.associatedActions != null)
        .map(wk => wk.associatedActions!.map(aa => ensureAssociatedActionWithWork(wk, aa)))
        .flatten<AssociatedActionWithWork>();
}
function ensureAssociatedActionWithWork(
    item: BaseOutcomeBasedWork,
    aa: number
): AssociatedActionWithWork {
    // NB we need a new object otherwise we update the same one - resulting in each row being the same
    // TODO move to Object.assign when IE support gone (JSON.parse/stringify is quite slow)
    // shallow copy fine
    const result = <AssociatedActionWithWork> JSON.parse(JSON.stringify(item));
    result.associatedActionId = aa;
    return result;
}

//*********************************
// EXPORT analyser - risk

const riskActionAdditionalColumns = columnMap(
    numberColumn<RiskActionEvidenceDto>("likelihood", row => row.likelihood),
    numberColumn<RiskActionEvidenceDto>("severity", row => row.severity),
    textColumn<RiskActionEvidenceDto>("hazard", row => row.hazard),
    textColumn<RiskActionEvidenceDto>("intervention", row => row.intervention)
);

export const riskActionOnlyColumns = joinColumnMaps(
    smartStepOnlyColumns,
    riskActionAdditionalColumns
);

export class RiskActionOnlyAnalysis extends SequenceAnalysis<RiskActionEvidenceDto> {
    constructor(ctx: AnalysisContext, data: Sequence<RiskActionEvidenceDto>) {
        super(ctx, data, (item: RiskActionEvidenceDto) => item.id!.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            RiskActionOnly: riskActionOnlyColumns,
            RiskActionWithWork: riskActionWithWorkColumns
        };
    }
}

export const flattenToRiskActionAnalyser: Transformer<RiskWorkEvidenceDto, RiskActionEvidenceDto> =
    function (
        ctx: AnalysisContext,
        input: Sequence<RiskWorkEvidenceDto>
    ): SequenceAnalysis<RiskActionEvidenceDto> {
        return new RiskActionOnlyAnalysis(
            ctx,
            flattenWithParentByFn(input, rw => rw.riskActions)
        );
    };
types.analysersByName["flattenToRiskActionAnalyser"] = flattenToRiskActionAnalyser;

const riskWorkColumns = columnMap(
    textColumn<RiskWorkEvidenceDto>("w-id", row => row.id),
    numberColumn<RiskWorkEvidenceDto>("sr-id", row => row.serviceRecipientId)
);

// if used flattenWithParentByFn
const riskActionToWork = joinNestedPathColumnMaps<
    EntityWithParent<RiskWorkEvidenceDto, RiskActionEvidenceDto>,
    RiskWorkEvidenceDto
>("w", row => row.parent, riskWorkColumns);
const riskActionWithWorkColumns = joinColumnMaps(riskActionOnlyColumns, riskActionToWork);

const riskAreaOnlyColumns = columnMap(
    numberColumn<RiskGroupEvidenceDto>("id", row => row.id),
    textColumn<RiskGroupEvidenceDto>("trigger", row => row.trigger),
    textColumn<RiskGroupEvidenceDto>("control", row => row.control),
    textColumn<RiskGroupEvidenceDto>("metric", row => row.levelMeasure),
    numberColumn<RiskGroupEvidenceDto>("metric level", row => row.level),
    numberColumn<RiskGroupEvidenceDto>("ra-id", row => row.riskAreaId),
    textColumn<RiskGroupEvidenceDto>("ra-name", row => row.riskAreaName)
);

export interface RiskGroupEvidenceDtoWithWork extends RiskGroupEvidenceDto {
    work: RiskWorkEvidenceDto;
}

const riskAreaToWorkColumns = joinNestedPathColumnMaps<
    RiskGroupEvidenceDtoWithWork,
    RiskWorkEvidenceDto
>("w", row => row.work, riskWorkColumns);
export const riskAreaWithWorkColumns = joinColumnMaps(riskAreaOnlyColumns, riskAreaToWorkColumns);

export class RiskAreaWithWorkAnalysis extends SequenceAnalysis<RiskGroupEvidenceDtoWithWork> {
    constructor(ctx: AnalysisContext, data: Sequence<RiskGroupEvidenceDtoWithWork>) {
        super(ctx, data, (item: RiskGroupEvidenceDtoWithWork) => item.id.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            RiskAreaWithWork: riskAreaWithWorkColumns
        };
    }
}
export const flattenToRiskAreaWithWorkAnalyser: Transformer<
    RiskWorkEvidenceDto,
    RiskGroupEvidenceDtoWithWork
> = function (
    ctx: AnalysisContext,
    input: Sequence<RiskWorkEvidenceDto>
): SequenceAnalysis<RiskGroupEvidenceDtoWithWork> {
    return new RiskAreaWithWorkAnalysis(ctx, flattenToRiskAreasWithWork(input));
};
types.analysersByName["flattenToRiskAreaAnalyser"] = flattenToRiskAreaWithWorkAnalyser;

export function flattenToRiskAreasWithWork(
    input: Sequence<RiskWorkEvidenceDto>
): Sequence<RiskGroupEvidenceDtoWithWork> {
    // allAreas
    return input
        .filter(work => work != null && work.riskAreas != null)
        .map(work => work.riskAreas.map(ra => ensureRiskAreaReferencesWork(work, ra)))
        .flatten<RiskGroupEvidenceDtoWithWork>();
}
function ensureRiskAreaReferencesWork(
    work: RiskWorkEvidenceDto,
    riskArea: RiskGroupEvidenceDto
): RiskGroupEvidenceDtoWithWork {
    const result = <RiskGroupEvidenceDtoWithWork>riskArea;
    result.work = work;
    return result;
}

/**
 * riskFlagOnly shows the flag information within a piece of work
 */
const riskFlagOnlyColumns = columnMap(
    numberColumn<FlagEvidenceDto>("id", row => row.id),
    numberColumn<FlagEvidenceDto>("f-id", row => row.flagId),
    textColumn<FlagEvidenceDto>("f-name", (row, ctx) =>
        ctx.getSessionData().getListDefinitionEntryById(row.flagId).getDisplayName()
    ),
    booleanColumn<FlagEvidenceDto>("value", row => row.value)
);

export interface FlagEvidenceDtoWithWork extends FlagEvidenceDto {
    work: RiskWorkEvidenceDto;
}

const riskFlagWorkColumns = columnMap(
    textColumn<FlagEvidenceDtoWithWork>("w-id", row => row.work.id)
);
export const riskFlagWithWorkColumns = joinColumnMaps(riskFlagOnlyColumns, riskFlagWorkColumns);

export class RiskFlagWithWorkAnalysis extends SequenceAnalysis<FlagEvidenceDtoWithWork> {
    constructor(ctx: AnalysisContext, data: Sequence<FlagEvidenceDtoWithWork>) {
        super(ctx, data, (item: FlagEvidenceDtoWithWork) => item.id.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            RiskFlagWithWork: riskFlagWithWorkColumns
        };
    }
}

export const flattenToRiskFlagWithWorkAnalyser: Transformer<
    RiskWorkEvidenceDto,
    FlagEvidenceDtoWithWork
> = function (
    ctx: AnalysisContext,
    input: Sequence<RiskWorkEvidenceDto>
): SequenceAnalysis<FlagEvidenceDtoWithWork> {
    return new RiskFlagWithWorkAnalysis(ctx, flattenToRiskFlagWithWork(input));
};
types.analysersByName["flattenToRiskFlagAnalyser"] = flattenToRiskFlagWithWorkAnalyser;

export function flattenToRiskFlagWithWork(
    input: Sequence<RiskWorkEvidenceDto>
): Sequence<FlagEvidenceDtoWithWork> {
    // allAreas
    return input
        .filter(work => work != null && work.flags != null)
        .map(work => work.flags.map(rf => ensureRiskFlagReferencesWork(work, rf)))
        .flatten<FlagEvidenceDtoWithWork>();
}
function ensureRiskFlagReferencesWork(
    work: RiskWorkEvidenceDto,
    flag: FlagEvidenceDto
): FlagEvidenceDtoWithWork {
    const result = <FlagEvidenceDtoWithWork>flag;
    result.work = work;
    return result;
}
