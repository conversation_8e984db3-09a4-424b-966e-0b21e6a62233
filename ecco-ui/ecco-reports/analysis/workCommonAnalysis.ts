///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import Lazy = require("lazy");
import types = require("./types");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Accumulator = types.Accumulator;
import GroupedAnalysis = types.GroupedAnalysis;
import WorkWithRefToReferralAggregate = types.WorkWithRefToReferralAggregate;
import Group = types.Group;
import SingleRecordAnalysis = types.SingleRecordAnalysis;
import ReferralAggregate = types.ReferralAggregate;
import Analyser = types.Analyser;
import extractPair = types.extractPair;
import GroupFn = types.GroupFn;
import * as commonTypes from "@eccosolutions/ecco-common";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import * as evidenceDto from "ecco-dto";
import {BaseActionInstance} from "ecco-dto";
import {BaseWork, FormEvidence, BaseOutcomeBasedWork, SmartStepStatusName} from "ecco-dto";
import * as evidenceRiskDto from "ecco-dto";
import {AnalysisContext} from "../chart-domain";
import {
    EntityType,
    entityUrl,
    evidenceTaskNameLookup,
    referralReportItemColumns,
    referralSummaryColumns
} from "../tables/predefined-table-representations";
import {SingleValueHistoryDto} from "ecco-dto";
import {CustomFormWorkWithRefToReferralAggregate, EntityWithParent} from "./types";
import {
    booleanColumn,
    columnMap,
    ColumnRepresentation,
    dateColumn,
    dateTimeColumn,
    dateTimeColumnFromIsoZoneless,
    fixedPrecisionNumberColumn,
    getPathValue,
    hrefColumn,
    HrefData,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    RowContext,
    textColumn
} from "../controls/tableSupport";
import {ReferralSummaryDto} from "ecco-dto";
import {ReferralAggregateAnalysis} from "./referralAnalysis";

export interface UnGroupFn<T> {
    (input: Sequence<Group<T>>): Sequence<T>;
}

export function unGroup<T>(input: Sequence<Group<T>>): Sequence<T> {
    return input.map(workWithRef => workWithRef.elements).flatten<T>();
}

export function workUnGroupBy<T>(input: Sequence<Group<T>>, unGroupFn: UnGroupFn<T>): Sequence<T> {
    return unGroupFn(input);
}

export function workCountsBy<T>(
    input: Sequence<T>,
    groupFn: GroupFn<T>,
    ctx?: AnalysisContext
): Sequence<Group<T>> {
    return groupFn(input, ctx).map(pair => {
        const result: Group<T> = {
            key: pair.key,
            elements: pair.elements,
            count: pair.elements.flatten().size()
        };
        return result;
    });
}

function convertToNumber<T>(input: T, property: keyof T): number {
    return Number(input[property]) || 0;
}

export function workCountsByWithCountOf<T>(input: Sequence<T>, groupFn: GroupFn<T>, property?: keyof T, ctx?: AnalysisContext): Sequence<Group<T>> {
    return groupFn(input, ctx)
            .map((pair) => {
                const result: Group<T> = {
                    key: pair.key,
                    elements: pair.elements,
                    count: property
                            ? pair.elements.reduce((prev, curr) => {
                                const c = convertToNumber(curr, property);
                                return prev + c;
                            }, 0)
                            : pair.elements.flatten().size()
                };
                return result;
            });
}

export function groupByCommentType<T extends BaseWork>(
    input: Sequence<T>,
    ctx?: AnalysisContext
): Sequence<Group<T>> {
    const nameLookup = (id: number) =>
        ctx!.getSessionData().getListDefinitionEntryById(id).getDisplayName();

    return input
        .groupBy(inputElement => nameLookup(inputElement.commentTypeId) || "no type")
        .pairs()
        .map(extractPair);
}

export function groupBySourceTask<T extends BaseWork>(input: Sequence<T>): Sequence<Group<T>> {
    return input
        .groupBy(inputElement =>
            evidenceTaskNameLookup(inputElement.taskName, inputElement.serviceAllocationId)
        )
        .pairs()
        .map(extractPair);
}
export function groupBySourceGroup(
    input: Sequence<WorkWithRefToReferralAggregate>
): Sequence<Group<WorkWithRefToReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.evidenceGroup.name)
        .pairs()
        .map(extractPair);
}

export function ensureWorkReferencesItem(
    item: ReferralAggregate,
    work: BaseWork
): WorkWithRefToReferralAggregate {
    const result = <WorkWithRefToReferralAggregate>work;
    result.reportItem = item;
    // evidenceGroup will be set elsewhere based on task type
    return result;
}
/** Produce a sequence of pairs of referral, work such that they can be grouped by entries relating to work */
export function flattenSupportWork(
    input: Sequence<ReferralAggregate>
): Sequence<WorkWithRefToReferralAggregate> {
    const allWork: Sequence<WorkWithRefToReferralAggregate> = input
        .filter(ra => ra != null && ra.supportWork != null)
        .map(ra =>
            ra
                .supportWork! // non-null from filter above
                .map(work => ensureWorkReferencesItem(ra, work))
        )
        .flatten<WorkWithRefToReferralAggregate>();
    return allWork;
}
export function flattenRiskWork(
    input: Sequence<ReferralAggregate>
): Sequence<WorkWithRefToReferralAggregate> {
    const allWork: Sequence<WorkWithRefToReferralAggregate> = input
        .filter(ra => ra != null)
        .map(ra =>
            ra
                .riskWork! // non-null from filter above
                .map(work => ensureWorkReferencesItem(ra, work))
        )
        .flatten<WorkWithRefToReferralAggregate>();
    return allWork;
}
export function flattenAllWork(
    input: Sequence<ReferralAggregate>
): Sequence<WorkWithRefToReferralAggregate> {
    return flattenSupportWork(input).union(flattenRiskWork(input).toArray());
}
/** Produce a sequence of pairs of referral, work such that they can be grouped by entries relating to work */
export function flattenCustomFormWork(
    input: Sequence<ReferralAggregate>
): Sequence<CustomFormWorkWithRefToReferralAggregate> {
    const allWork: Sequence<CustomFormWorkWithRefToReferralAggregate> = input
        .filter(ra => ra != null)
        .map(ra => {
            if (ra.customFormWorkLatest == null) {
                // fake a CustomFormWorkWithRefToRA to the extent that customFormWorkOnlyColumns requires
                const workWithRA: Partial<CustomFormWorkWithRefToReferralAggregate> = {
                    reportItem: ra
                };
                return workWithRA;
            } else {
                return ensureCustomFormWorkReferencesItem(ra, ra.customFormWorkLatest!); // non-null from filter above
            }
        })
        .flatten<CustomFormWorkWithRefToReferralAggregate>();
    return allWork;
}
export function ensureCustomFormWorkReferencesItem(
    item: ReferralAggregate,
    work: FormEvidence<any>
): CustomFormWorkWithRefToReferralAggregate {
    const result = <CustomFormWorkWithRefToReferralAggregate>work;
    result.reportItem = item;
    return result;
}

//*********************************
// retainLatestActions - functions to retain only the latest EvidenceAction's within an ordered sequence of BaseWork
//*********************************

/** intermediate working objects */
interface EvidenceActionWithRefToWork extends EvidenceAction {
    workItem: BaseWork;
}
interface EvidenceAction extends evidenceDto.SupportAction {}

export interface BaseWorkWithActions extends BaseWork {
    actions: EvidenceAction[];
    flags?: evidenceDto.FlagEvidenceDto[];
    riskActions?: evidenceRiskDto.RiskActionEvidenceDto[];
    riskAreas?: evidenceRiskDto.RiskGroupEvidenceDto[];
}

/** build the working object */
function ensureActionReferencesItem(
    action: EvidenceAction,
    work: BaseWork
): EvidenceActionWithRefToWork {
    // cast the action to another object
    const result = <EvidenceActionWithRefToWork>action;
    result.workItem = work;
    return result;
}

/** convert the Work into EvidenceActionWithRefToWork intermediate working object */
function convertToEvidenceActionWithRefToWork(
    input: Sequence<BaseWorkWithActions>
): Sequence<EvidenceActionWithRefToWork> {
    const allActions: Sequence<EvidenceActionWithRefToWork> = input
        .filter(work => work != null && work.actions != null)
        // for each work
        .map(work =>
            work.actions
                // for each action, reference the work
                .map(action => ensureActionReferencesItem(action, work))
        )
        .flatten<EvidenceActionWithRefToWork>();
    return allActions;
}

/** group the intermediate object by work */
function groupByWorkId(
    input: Sequence<EvidenceActionWithRefToWork>
): Sequence<Group<EvidenceActionWithRefToWork>> {
    return input
        .groupBy(inputElement => inputElement.workItem.id || "error: no work.id")
        .pairs()
        .map(extractPair);
}
/** group the intermediate object by status */
function groupByStatusId(
    input: Sequence<EvidenceActionWithRefToWork>
): Sequence<Group<EvidenceActionWithRefToWork>> {
    return input
        .groupBy(inputElement => inputElement.status.toString() || "no source task")
        .pairs()
        .map(extractPair);
}
/** group the intermediate object by action def id */
function groupByActionId(
    input: Sequence<EvidenceActionWithRefToWork>
): Sequence<Group<EvidenceActionWithRefToWork>> {
    // groupBy takes the return value as the key with an array of the matches
    return (
        input
            .groupBy(inputElement => inputElement.actionId.toString())
            // convert into a sequence
            .pairs()
            // extractPair into a Group<EvidenceAction> (which is key / elements)
            .map(extractPair)
    );
}

// filter and group by need to be together since its difficult to filter first if the child makes a decision on the parent
// and its difficult to filter second because it involves filtering, then migrating to a group (a group, a filter, a group)
// but its easy to work on the pair ungrouped
export function filterWorkBySVHDate(
    input: Sequence<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>
): Sequence<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}> {
    return input
        .filter(svhWithWorkRef => {
            let validFrom = EccoDateTime.parseIso8601Utc(svhWithWorkRef.svh.validFrom);
            let validTo = EccoDateTime.parseIso8601Utc(svhWithWorkRef.svh.validTo);
            let workDate = EccoDateTime.parseIso8601Utc(svhWithWorkRef.work.workDate);

            // include if the work is after the date
            let match =
                workDate.laterThanOrEqual(validFrom) &&
                (validTo ? workDate.earlierThan(validTo) : true);
            //console.log("workDate is between? "+match);
            return match;
        })
        .filter(svhWithWorkRef => svhWithWorkRef != null);
}
// get a work item per svh
export function svhWithWork(
    input: Sequence<WorkWithRefToReferralAggregate>
): Sequence<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}> {
    return input
        .map(work => {
            return work.reportItem.singleValueHistory!.map(svh => {
                return {svh: svh, work: work};
            });
        })
        .flatten<{svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}>();
}

/** convert the intermediate back into BaseWork whilst retaining work items that did not get filtered
 *  (ie don't lose work items just because there were no actions in the work)
 */
function convertToBaseWork<T extends BaseWorkWithActions>(
    input: Sequence<T>,
    intermediates: Sequence<EvidenceActionWithRefToWork>
): Sequence<T> {
    const groupOfWorkIdToIntermediates = groupByWorkId(intermediates);

    return input
        .map(work => {
            // no actions, no filtering needed
            if (work.actions.length == 0) {
                return work;

                // replace the actions with no actions, or our sanitised actions
            } else {
                // see if the work.id matches the group.key, then get the first() to get the single group of workId
                const matchedIntermediateMaybe: Group<EvidenceActionWithRefToWork> =
                    groupOfWorkIdToIntermediates
                        .filter(groupOfWorkId => groupOfWorkId.key == work.id)
                        .first();

                // replace actions with the efforts of our work
                // this is destructive - we could clone work first
                if (matchedIntermediateMaybe) {
                    work.actions = <EvidenceAction[]>matchedIntermediateMaybe.elements.toArray();
                } else {
                    // if no matches, then no statusChanged actions, so clear them
                    work.actions = [];
                }
                return work;
            }
        })
        .flatten<T>();
}

/////////////////////////////////
// HARD WIRED LOGGING
export var hardWiredLogActionsId = 104;
// we can compare this with the web-api input
export function logIntermediary(
    input: Sequence<EvidenceActionWithRefToWork>
): Sequence<EvidenceActionWithRefToWork> {
    input
        .filter(ea => ea.actionId == hardWiredLogActionsId)
        .each(ea =>
            console.log(
                "ReportDataSourceFactory.retainChangedActions log intermediary for specific actionId %o",
                ea
            )
        );
    return input;
}
// we can compare this with the log intermediary
export function logConstructedWork(
    input: Sequence<BaseWorkWithActions>
): Sequence<BaseWorkWithActions> {
    input.each(bw => {
        if (bw != null && bw.actions != null) {
            if (bw.actions.some(a => a.actionId == hardWiredLogActionsId)) {
                console.log(
                    "ReportDataSourceFactory.retainChangedActions log result where specific actionId %o",
                    bw
                );
            }
        }
    });
    return input;
}
/////////////////////////////////

export function validateConstructedWork(input: Sequence<BaseWorkWithActions>) {
    input
        .map(bw => bw.actions)
        .filter(actionsA => actionsA != null && actionsA.some(a => !a.statusChange))
        .each(actionsA => {
            console.log("validateConstructedWork: Work action has statusChange 0: %o", actionsA);
            throw new Error("validateConstructedWork: failed");
        });
}

/** Takes work items and removes actions which weren't part of a status change */
export function retainChangedActions<T extends BaseWorkWithActions>(
    input: Sequence<T>
): Sequence<T> {
    // export function retainChangedActions(input: Sequence<BaseWorkWithActions>): Sequence<BaseWorkWithActions> {

    const resultOfIntermediates: Sequence<EvidenceActionWithRefToWork> =
        // convert to smart steps with ref to work
        convertToEvidenceActionWithRefToWork(input).filter(
            actionWithWork => actionWithWork != null && actionWithWork.statusChange
        );

    logIntermediary(resultOfIntermediates);

    // convert the result back into SupportWork
    const result = convertToBaseWork(input, resultOfIntermediates);

    validateConstructedWork(result);

    logConstructedWork(result);

    return result;
}

export function riskRetainChangedActions(
    input: Sequence<evidenceRiskDto.RiskWorkEvidenceDto>
): Sequence<evidenceRiskDto.RiskWorkEvidenceDto> {
    return convertBaseWorkWithActionsToRisk(
        retainChangedActions(convertRiskToBaseWorkWithActions(input))
    );
}

/** convert the intermediate to/fro BaseWork */
function convertRiskToBaseWorkWithActions(
    input: Sequence<evidenceRiskDto.RiskWorkEvidenceDto>
): Sequence<BaseWorkWithActions> {
    return input
        .map(riskWork => {
            //var work: BaseWorkWithActions = <BaseWorkWithActions>riskWork;
            const work: BaseWorkWithActions = {
                id: riskWork.id.toString(),
                requestedDelete: riskWork.requestedDelete,
                serviceRecipientId: riskWork.serviceRecipientId,
                parentCode: riskWork.parentCode,
                parentPrefix: riskWork.parentPrefix,
                serviceAllocationId: riskWork.serviceAllocationId,
                authorDisplayName: riskWork.authorDisplayName,
                comment: riskWork.comment,
                createdDate: riskWork.createdDate,
                workDate: riskWork.workDate,
                commentTypeId: riskWork.commentTypeId,
                minsSpent: riskWork.minsSpent,
                signatureId: riskWork.signatureId,
                taskName: riskWork.taskName,
                actions: <EvidenceAction[]>riskWork.riskActions,

                // retain the extra risk properties
                riskActions: riskWork.riskActions,
                flags: riskWork.flags,
                riskAreas: riskWork.riskAreas
            };
            return work;
        })
        .flatten<BaseWorkWithActions>();
}
function convertBaseWorkWithActionsToRisk(
    input: Sequence<BaseWorkWithActions>
): Sequence<evidenceRiskDto.RiskWorkEvidenceDto> {
    return input
        .map(baseWork => {
            const work: evidenceRiskDto.RiskWorkEvidenceDto = {
                id: baseWork.id.toString(),
                requestedDelete: baseWork.requestedDelete,
                serviceRecipientId: baseWork.serviceRecipientId,
                serviceAllocationId: baseWork.serviceAllocationId,
                parentCode: baseWork.parentCode,
                parentPrefix: baseWork.parentPrefix,
                authorDisplayName: baseWork.authorDisplayName,
                comment: baseWork.comment,
                createdDate: baseWork.createdDate,
                workDate: baseWork.workDate,
                commentTypeId: baseWork.commentTypeId,
                minsSpent: baseWork.minsSpent,
                signatureId: baseWork.signatureId,
                taskName: baseWork.taskName,
                riskActions: <evidenceRiskDto.RiskActionEvidenceDto[]>baseWork.actions,

                flags: baseWork.flags!, // ! usage (not checked)
                riskAreas: baseWork.riskAreas! // ! usage (not checked)
            };
            return work;
        })
        .flatten<evidenceRiskDto.RiskWorkEvidenceDto>();
}

//*********************************
// Analyers for types.ReferralAggregateGroupWithVisits

/** This deals with clicking on chart segment */
const WrapReferralAggregateAnalysisWithVisitsAnalyser: Analyser<
    types.ReferralAggregateGroupWithVisits,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: types.ReferralAggregateGroupWithVisits
): ReferralAggregateAnalysisWithVisits {
    return new ReferralAggregateAnalysisWithVisits(ctx, input);
};
function latestDate(date1: EccoDate | null, date2: EccoDate | null): EccoDate | null {
    if (!date1) {
        return date2;
    }
    if (!date2) {
        return date1;
    }
    return date1.laterThan(date2) ? date1 : date2;
}
/** This deals with clicking on select all segment */
function reduceGroupedVisitsToSingle(
    input: Sequence<types.ReferralAggregateGroupWithVisits>
): types.ReferralAggregateGroupWithVisits {
    const memo: types.ReferralAggregateGroupWithVisits = {
        key: null!,
        latestWorkDate: null,
        lastSignedWorkDate: null,
        lastUnSignedWorkDate: null,
        totalTimeSpentMins: 0,
        totalVisits: 0,
        unsignedWorkCount: 0,
        averageVisitLength: 0,
        countByCommentType: {},
        elements: Lazy([]), // will map from unique items when done
        count: 0
    };
    return input.reduce((merge, data: types.ReferralAggregateGroupWithVisits) => {
        //merge.key =d
        //countByCommentType =
        merge.lastSignedWorkDate = latestDate(data.lastSignedWorkDate, merge.lastSignedWorkDate);
        merge.lastUnSignedWorkDate = latestDate(
            data.lastUnSignedWorkDate,
            merge.lastUnSignedWorkDate
        );
        merge.latestWorkDate = latestDate(data.latestWorkDate, merge.latestWorkDate);
        merge.totalTimeSpentMins += data.totalTimeSpentMins;
        merge.totalVisits += data.totalVisits;
        merge.unsignedWorkCount += data.unsignedWorkCount;
        merge.averageVisitLength = merge.totalTimeSpentMins / merge.totalVisits;
        merge.elements =
            merge.elements && data.elements && merge.elements.concat(data.elements || []);
        merge.count += data.count;
        return merge;
    }, memo);
}

const MergeReferralAggregateAnalysisWithVisitsAnalyser: Analyser<
    Sequence<types.ReferralAggregateGroupWithVisits>,
    types.ReferralAggregateGroupWithVisits
> = function (
    ctx: AnalysisContext,
    input: Sequence<types.ReferralAggregateGroupWithVisits>
): ReferralAggregateAnalysisWithVisits {
    return new ReferralAggregateAnalysisWithVisits(ctx, reduceGroupedVisitsToSingle(input));
};
/**
 * TODO This is an ideal candidate for understanding why we need a bit more knowledge of the parent in the analysers.
 * Without knowledge of the group, we end up with the columnMap repeating row.key for each possible key
 * and code like the below which is used to extract properties based on that knowledge.
 * One alternative is to have separate columnMaps and analysers for each key - which isn't ideal and we'd like to keep
 * each analsyer small and consise without expert knowledge of the previous steps. It would be nice to re-use separate
 * columnMaps that know about their object.
 */
function extractClientFrom_RAGWV_groupedByRid(
    row: types.ReferralAggregateGroupWithVisits
): string | null {
    if (row.elements.size() == 0) {
        return null;
    } else {
        const ref = row.elements.first(1).toArray()[0].referral;
        return ref.clientCode || ref.clientId.toString();
    }
}
function extractClientNameFrom_RAGWV_groupedByRid(
    row: types.ReferralAggregateGroupWithVisits
): string | null {
    if (row.elements.size() == 0) {
        return null;
    } else {
        const ref = row.elements.first(1).toArray()[0].referral;
        return ref.clientDisplayName;
    }
}
function extractWorkerNameFrom_RAGWV_groupedByRid(
    row: types.ReferralAggregateGroupWithVisits
): string | null {
    if (row.elements.size() == 0) {
        return null;
    } else {
        const ref = row.elements.first(1).toArray()[0].referral;
        return ref.supportWorkerDisplayName;
    }
}
function extractReferralAggFrom_RAGWV_groupedByRid(
    row: types.ReferralAggregateGroupWithVisits
): ReferralAggregate | null {
    if (row.elements.size() == 0) {
        return null;
    } else {
        return row.elements.first(1).toArray()[0];
    }
}
function extractParent<P, C extends {parent: P}>(row: types.Group<C>): P | null {
    if (row.elements.size() == 0) {
        return null;
    } else {
        const c = row.elements.first(1).toArray()[0];
        return c.parent;
    }
}
export function hoursSpent(minsSpent: number): number {
    if (minsSpent > 0) {
        const hours = minsSpent / 60;
        return Math.floor(hours);
    }
    return 0;
}

export function minsInLastHour(minsSpent: number): number {
    if (minsSpent > 0) {
        return minsSpent % 60;
    }
    return 0;
}

const workHref: (work: BaseWork) => HrefData = work => {
    return {
        display: work.id,
        url: entityUrl(work.id, EntityType.SupportWork, work.serviceRecipientId.toString())
    } as HrefData;
};
export const baseWorkOnlyColumns = columnMap(
    numberColumn<BaseWork>("sr-id", row => row.serviceRecipientId),
    hrefColumn<BaseWork>("w-id", row => workHref(row)),
    textColumn<BaseWork>("worker", row => "-incorrect def-"), // this was 'authorDisplayName' but we expect 'r: worker'
    dateTimeColumnFromIsoZoneless<BaseWork>("created", row => row.createdDate),
    dateTimeColumnFromIsoZoneless<BaseWork>("work date", row => row.workDate),
    dateTimeColumnFromIsoZoneless<BaseWork>("created (UTC)", row => row.createdDate), // FIXME: We need createdDate to be sent with Z on on end (as it is UTC in database)
    dateTimeColumnFromIsoZoneless<BaseWork>("work date (clock time)", row => row.workDate),
    textColumn<BaseWork>("type", (row, ctx) =>
        ctx.getSessionData().getListDefinitionEntryById(row.commentTypeId).getDisplayName()
    ),
    textColumn<BaseWork>("comment", row => row.comment),
    numberColumn<BaseWork>("time (mins)", row => row.minsSpent),
    numberColumn<BaseWork>("time (hr)", row => hoursSpent(row.minsSpent)),
    numberColumn<BaseWork>("time (mins left)", row => minsInLastHour(row.minsSpent)),
    textColumn<BaseWork>("task", row =>
        evidenceTaskNameLookup(row.taskName, row.serviceAllocationId)
    ),
    booleanColumn<BaseWork>("signed", row => !!row.signatureId),
    textColumn<BaseWork>("author", row => row.authorDisplayName)
);

const baseOutcomeWorkOnlyColumns = columnMap(
    textColumn<BaseOutcomeBasedWork>("eventId", row => row.eventId),
    textColumn<BaseOutcomeBasedWork>("meeting status", (row, ctx) =>
        row.meetingStatusId
            ? ctx.getSessionData().getListDefinitionEntryById(row.meetingStatusId).getName()
            : null
    ),
    textColumn<BaseOutcomeBasedWork>("client status", (row, ctx) =>
        row.clientStatusId
            ? ctx.getSessionData().getListDefinitionEntryById(row.clientStatusId).getName()
            : null
    ),
    textColumn<BaseOutcomeBasedWork>("location", (row, ctx) =>
        row.locationId
            ? ctx.getSessionData().getListDefinitionEntryById(row.locationId).getName()
            : null
    ),
    numberColumn<BaseOutcomeBasedWork>("travel time (mins)", row => row.minsTravel),
    numberColumn<BaseOutcomeBasedWork>("mileage to/from", row => row.mileageTo),
    numberColumn<BaseOutcomeBasedWork>("mileage during visit", row => row.mileageDuring)
);

export const baseOutcomeWorkColumns = joinColumnMaps(
    baseWorkOnlyColumns,
    baseOutcomeWorkOnlyColumns
);

function lookupActionStatusName(id: number) {
    return id ? SmartStepStatusName[id] : "-";
}

export const smartStepOnlyColumns = columnMap(
    numberColumn<BaseActionInstance>("id", row => row.id), // database instance id
    textColumn<BaseActionInstance>("instance-id", row => row.actionInstanceUuid),
    textColumn<BaseActionInstance>("h-name", row =>
        row.hierarchy == null ? "-" : row.hierarchy == 0 ? "action" : `sub-action ${row.hierarchy}`
    ),
    numberColumn<BaseActionInstance>("hierarchy", row => row.hierarchy),
    textColumn<BaseActionInstance>("position", row => row.position),
    textColumn<BaseActionInstance>("w-id", row => row.workId),
    dateTimeColumn<BaseActionInstance>("workDate", row => EccoDateTime.parseIso8601(row.workDate)),
    numberColumn<BaseActionInstance>("a-id", row => row.actionId),
    numberColumn<BaseActionInstance>("a-group-id", row => row.actionGroupId),
    numberColumn<BaseActionInstance>("o-id", row => row.outcomeId),
    textColumn<BaseActionInstance>("o-name", (row, ctx) => {
        return ctx.getSessionData().getAnyActionById(row.actionId).getOutcome().getName();
    }),
    textColumn<BaseActionInstance>("a-name", row => row.name),
    textColumn<BaseActionInstance>("a-status", row => lookupActionStatusName(row.status)),
    dateColumn<BaseActionInstance>("expiry date", row => EccoDate.parseIso8601(row.expiryDate)),
    dateColumn<BaseActionInstance>(
        "target date",
        row => EccoDateTime.parseIso8601(row.targetDateTime)?.toEccoDate() ?? null
    ),
    textColumn<BaseActionInstance>("target schedule", row => row.targetSchedule),
    // NB check date is only the FIRST of the day unless we have used expandByScheduleTime
    dateTimeColumn<BaseActionInstance>("check date", row =>
        EccoDateTime.parseIso8601(row.targetDateTime)
    ),
    textColumn<BaseActionInstance>("goal name", row => row.goalName),
    textColumn<BaseActionInstance>("goal plan", row => row.goalPlan),
    booleanColumn<BaseActionInstance>("status change", row => row.statusChange),
    numberColumn<BaseActionInstance>("reason-id", row => row.statusChangeReasonId),
    textColumn<BaseActionInstance>("reason", (row, ctx) =>
        ctx.getSessionData().getListDefinitionEntryById(row.statusChangeReasonId)?.getName()
    ),
    textColumn<BaseActionInstance>("score", (row, ctx) =>
        row.score || row.score == 0
            ? row.score <= 100
                ? row.score.toString()
                : ctx.getSessionData().getListDefinitionEntryById(row.score).getName()
            : "-"
    )
);

const visitGroupColumns = columnMap(
    // use only one of these key representations
    textColumn<types.ReferralAggregateGroupWithVisits>("client", row => row.key),
    textColumn<types.ReferralAggregateGroupWithVisits>("r-id", row => row.key),
    // c-id is only valid here if the above r-id is the key
    // because we know we have grouped around the referral, we know the ReferralAggregate elements will be the same client
    textColumn<types.ReferralAggregateGroupWithVisits>("c-id", row =>
        extractClientFrom_RAGWV_groupedByRid(row)
    ),
    // client is only valid here if the above r-id is the key
    // NB its "client name" to differentiate from the other client
    textColumn<types.ReferralAggregateGroupWithVisits>("client name", row =>
        extractClientNameFrom_RAGWV_groupedByRid(row)
    ),
    // worker is only valid here if the above r-id is the key
    textColumn<types.ReferralAggregateGroupWithVisits>("worker", row =>
        extractWorkerNameFrom_RAGWV_groupedByRid(row)
    ),
    textColumn<types.ReferralAggregateGroupWithVisits>("work author", row => row.key)
);

const visitOnlyColumns = columnMap(
    // other columns
    numberColumn<types.WorkAnalysis>("time spent", row => row.totalTimeSpentMins),
    numberColumn<types.WorkAnalysis>("time (hr)", row => hoursSpent(row.totalTimeSpentMins)),
    numberColumn<types.WorkAnalysis>("time (mins left)", row =>
        minsInLastHour(row.totalTimeSpentMins)
    ),
    numberColumn<types.WorkAnalysis>("visits", row => row.totalVisits),
    fixedPrecisionNumberColumn<types.WorkAnalysis>(
        "average time spent",
        2,
        row => row.averageVisitLength
    ),
    dateColumn<types.WorkAnalysis>("latest work", row => row.latestWorkDate),
    dateColumn<types.WorkAnalysis>("last signed work", row => row.lastSignedWorkDate),
    dateColumn<types.WorkAnalysis>("last unsigned work", row => row.lastUnSignedWorkDate),
    textColumn<types.WorkAnalysis>("breakdown", row => JSON.stringify(row.countByCommentType))
);
const visitToReferralAggColumns = joinNestedPathColumnMaps<
    types.ReferralAggregateGroupWithVisits,
    ReferralAggregate | null
>("r", row => extractReferralAggFrom_RAGWV_groupedByRid(row), referralReportItemColumns);

/*
Within the ReferralAggregateGroupWithVisits we want access to referral columns by objects, as below.
{
    "title": "household type",
    "representation": "o: lookup",
    "path": [
        "referral",
        "choicesMap",
        "householdType",
        "name"
    ]
},
Using 'r: ' is tricky since its a string not an object, so joinNestedPathColumnMaps would need to defer to expandedColumnRepresentation.
However, we can create a string reference ('lookup') to call object resolution specifically.
NB This could be improved by amending joinNestedPathColumnMaps - perhaps by simply appending 'lookup' to each map.
*/
const dummyObjectColumn: ColumnRepresentation<ReferralAggregate> = {
    getHeading() {
        return "lookup";
    }, // get the ReferralAggregate object simply by using this as a dummy field object
    getType() {
        return "string";
    },
    getCssClasses(row: ReferralAggregate) {
        return "";
    },
    represent(ctx: RowContext, TRow, substitutePath?: string[]) {
        return getPathValue(TRow, ctx, substitutePath!);
    }
};
const dummyObjectColumns = columnMap(dummyObjectColumn);
// lookup is only valid here if the above r-id is the key
const visitToReferralObjectColumns = joinNestedPathColumnMaps<
    types.ReferralAggregateGroupWithVisits,
    ReferralAggregate | null
>("o", row => extractReferralAggFrom_RAGWV_groupedByRid(row), dummyObjectColumns);

const visitsColumns = joinColumnMaps(
    visitGroupColumns,
    visitOnlyColumns,
    visitToReferralAggColumns,
    visitToReferralObjectColumns
);

// the visit info is at the group level, and the group is the evidence with .parent (see visitSupportRiskCounts and flattenWithParentByFn)
// so we just need to extract the first of the work, and find the parent
const visitToReferralColumns = joinNestedPathColumnMaps<
    types.GroupWithVisits<EntityWithParent<ReferralSummaryDto, evidenceDto.BaseOutcomeBasedWork>>,
    ReferralSummaryDto | null
>(
    "r",
    row => {
        return row.elements.size() == 0 ? null : row.elements.first(1).toArray()[0].parent;
    },
    referralSummaryColumns
);
export const visitWithReferralColumns = joinColumnMaps(visitOnlyColumns, visitToReferralColumns);

export class GroupedAnalysisWithVisits<T extends BaseWork> extends GroupedAnalysis<
    T,
    types.GroupWithVisits<T>
> {
    constructor(ctx: AnalysisContext, data: Sequence<types.GroupWithVisits<T>>) {
        super(ctx, data);
        this.recordRepresentation = {
            VisitOnlyColumns: visitOnlyColumns,
            VisitWithReferralColumns: visitWithReferralColumns
        };
        this.addOnClickAnalyser("ungroup", "Un_GroupedAnalysisWithVisitsAnalyser");
        // this.addOnClickManyAnalysis("ungroup", "ReferralAggregateAnalysis");
        // this.addOnClickAnalyser("single", WrapReferralAggregateAnalysisWithVisitsAnalyser);
    }
}

// this is where we end up, but this is Referral based, so we can't extract the same things we just clicked on in the visit!
export class GroupedReferralAggregateAnalysisWithVisits extends GroupedAnalysis<
    ReferralAggregate,
    types.ReferralAggregateGroupWithVisits
> {
    constructor(ctx: AnalysisContext, data: Sequence<types.ReferralAggregateGroupWithVisits>) {
        super(ctx, data);
        this.recordRepresentation = {
            VisitsAnalysis: visitsColumns
        };
        this.addOnClickAnalyser("ungroup", "WrapUnGroupReferralAggregateSequenceAnalyser");
        this.addOnClickManyAnalysis("ungroup", "ReferralAggregateAnalysis");
        this.addOnClickAnalyser("single", WrapReferralAggregateAnalysisWithVisitsAnalyser);
    }
}

export interface WorkWithRefGroupWithSummary extends Group<WorkWithRefToReferralAggregate> {
    count: number;
    totalTimeSpentMins: number;
}

//*********************************
// produce ReferralAggregateGroupWithCount via Group<WorkWithRefToReferralAggregate>
class ReferralAggregateAnalysisWithVisits extends SingleRecordAnalysis<types.ReferralAggregateGroupWithVisits> {
    constructor(ctx: AnalysisContext, data: types.ReferralAggregateGroupWithVisits) {
        super(ctx, data);
        this.recordRepresentation = {
            VisitsAnalysis: visitsColumns
        };
    }
}

//*********************************
// Accumulator for types.ReferralAggregateGroupWithVisits

export function accumulateBaseWorkToVisitsAnalysis<
    U extends BaseWork,
    R,
    T extends types.GroupWithVisits<R>
>(prev: T, work: U, ctx?: AnalysisContext): T {
    const workDate = EccoDateTime.parseIso8601(work.workDate).toEccoDate();
    if (!prev.latestWorkDate || workDate.laterThan(prev.latestWorkDate)) {
        prev.latestWorkDate = workDate;
    }

    if (work.signatureId) {
        if (!prev.lastSignedWorkDate || workDate.laterThan(prev.lastSignedWorkDate)) {
            prev.lastSignedWorkDate = workDate;
        }
    } else {
        if (!prev.lastUnSignedWorkDate || workDate.laterThan(prev.lastUnSignedWorkDate)) {
            prev.lastUnSignedWorkDate = workDate;
        }
    }

    prev.totalTimeSpentMins += work.minsSpent;
    prev.totalVisits += 1;

    let commentType = "-";
    if (work.commentTypeId) {
        const type = ctx!.getSessionData().getListDefinitionEntryById(work.commentTypeId);
        commentType = type.getDisplayName();
    }
    if (prev.countByCommentType[commentType]) {
        prev.countByCommentType[commentType]++;
    } else {
        prev.countByCommentType[commentType] = 1;
    }
    return prev;
}
interface ReferralAggregateGroupWithVisitsUniqueItems
    extends types.ReferralAggregateGroupWithVisits {
    uniqueItems: commonTypes.SparseArray<ReferralAggregate>;
}
/** Accumulate analysis where we've flattened to Work items where we then need to deduplicate the
 * related ReportItems for the next stage
 */
export class VisitsAnalysisFromWorkAccumulator
    implements Accumulator<types.ReferralAggregateGroupWithVisits, WorkWithRefToReferralAggregate>
{
    private memo: ReferralAggregateGroupWithVisitsUniqueItems;
    constructor(key: string, private ctx: AnalysisContext) {
        this.memo = {
            key: key,
            latestWorkDate: null,
            lastSignedWorkDate: null,
            lastUnSignedWorkDate: null,
            totalTimeSpentMins: 0,
            totalVisits: 0,
            unsignedWorkCount: 0,
            averageVisitLength: 0,
            countByCommentType: {},
            uniqueItems: {},
            elements: Lazy([]), // will map from unique items when done
            count: 0
        };
    }
    private accumulate(
        prev: ReferralAggregateGroupWithVisitsUniqueItems,
        work: WorkWithRefToReferralAggregate
    ): ReferralAggregateGroupWithVisitsUniqueItems {
        // NB this assumes the incoming grouping is by r-id
        prev.uniqueItems[work.reportItem.referral.referralId] = work.reportItem;
        return accumulateBaseWorkToVisitsAnalysis(prev, work, this.ctx);
    }
    private postProcess(): void {
        this.memo.elements = <Sequence<ReferralAggregate>>Lazy(this.memo.uniqueItems).values();
        this.memo.averageVisitLength = this.memo.totalTimeSpentMins / this.memo.totalVisits;
    }
    reduce(
        allWork: Sequence<WorkWithRefToReferralAggregate>
    ): types.ReferralAggregateGroupWithVisits {
        const result = allWork.reduce((prev, work) => this.accumulate(prev, work), this.memo);
        this.postProcess();
        return result;
    }
}

//*********************************
// Accumulator for WorkAccumulator

export interface GroupWithWorkWithRef {
    (input: Sequence<WorkWithRefToReferralAggregate>): Sequence<
        Group<WorkWithRefToReferralAggregate>
    >;
}

// a generic accumulator of work summary data
// GroupedReferralAggregateAnalysisWithVisits is tied to referral aggregate
// and the workAnalysis.workCountsBy produces WorkWithRefGroupWithCount which
// merely has a count on it. So we are left creating our own WorkWithRefGroupWithSummary.
// (we could have reused WorkWithRefGroupWithCount but easier to create new)
export function workCalcsPerGroup(
    input: Sequence<WorkWithRefToReferralAggregate>,
    groupFn: GroupWithWorkWithRef
): Sequence<WorkWithRefGroupWithSummary> {
    return groupFn(input).map(groupWork => {
        let work: Sequence<WorkWithRefToReferralAggregate> = groupWork.elements;
        return new WorkSummaryAccumulator(groupWork.key).reduce(work);
    });
}

export class WorkSummaryAccumulator
    implements Accumulator<WorkWithRefGroupWithSummary, WorkWithRefToReferralAggregate>
{
    private memo: WorkWithRefGroupWithSummary;
    constructor(key: string) {
        this.memo = {
            key: key,
            count: 0,
            totalTimeSpentMins: 0,
            elements: Lazy([])
        };
    }
    private accumulate(
        prev: WorkWithRefGroupWithSummary,
        work: WorkWithRefToReferralAggregate
    ): WorkWithRefGroupWithSummary {
        prev.elements = prev.elements.union(work);
        prev.count++;
        prev.totalTimeSpentMins += work.minsSpent;
        return prev;
    }
    reduce(allWork: Sequence<WorkWithRefToReferralAggregate>): WorkWithRefGroupWithSummary {
        const result = allWork.reduce((prev, work) => this.accumulate(prev, work), this.memo);
        return result;
    }
}

types.analysersByName["WrapReferralAggregateAnalysisWithVisitsAnalyser"] =
    WrapReferralAggregateAnalysisWithVisitsAnalyser;
types.analysersByName["MergeReferralAggregateAnalysisWithVisitsAnalyser"] =
    MergeReferralAggregateAnalysisWithVisitsAnalyser;
