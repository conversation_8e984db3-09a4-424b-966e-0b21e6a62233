///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import Lazy = require("lazy");
import types = require("./types");
import referralCommonAnalysis = require("./referralCommonAnalysis");
import tableRepresentations = require("../tables/predefined-table-representations");

import actionDefAnalysis = require("./actionDefAnalysis");
import activityAnalysis = require("./activityAnalysis");
import hactAnalysis = require("./hactAnalysis");
import smartStepCountsAnalysis = require("./smartStepCountsAnalysis");
import smartStepAnalysis = require("./smartStepAnalysis");
import visitAnalysis = require("./visitAnalysis");
import workAnalysis = require("./workAnalysis");
import questionnaireAnalysis = require("./questionnaireAnalysis");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Analyser = types.Analyser;
import Group = types.Group;
import GroupedAnalysis = types.GroupedAnalysis;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import extractPair = types.extractPair;
import GroupFn = types.GroupFn;
import Accumulator = types.Accumulator;
import groupByReferralStatus = referralCommonAnalysis.groupByReferralStatus;
import groupByExitReason = referralCommonAnalysis.groupByExitReason;
import groupByLatestClientStatus = referralCommonAnalysis.groupByLatestClientStatus;
import groupBySignpostReason = referralCommonAnalysis.groupBySignpostReason;
import groupByAgeAtDateOfReferral = referralCommonAnalysis.groupByAgeAtDateOfReferral;
import groupByReferredService = referralCommonAnalysis.groupByReferredService;
import groupByReferralProjectName = referralCommonAnalysis.groupByReferralProjectName;
import groupByReferralAssignedWorker = referralCommonAnalysis.groupByReferralAssignedWorker;
import groupByEthnicity = referralCommonAnalysis.groupByEthnicity;
import groupByGender = referralCommonAnalysis.groupByGender;
import groupByReligion = referralCommonAnalysis.groupByReligion;
import groupBySexualOrientation = referralCommonAnalysis.groupBySexualOrientation;
import groupByFirstLanguage = referralCommonAnalysis.groupByFirstLanguage;
import groupByDisability = referralCommonAnalysis.groupByDisability;
import groupByLengthOnService = referralCommonAnalysis.groupByLengthOnService;
import groupByLengthOnWaiting = referralCommonAnalysis.groupByLengthOnWaiting;
import {EccoDate} from "@eccosolutions/ecco-common";
import {lengthOfDaysOnWaiting} from "../tables/predefined-table-representations";
import {calendarEventsFromReferralAggregateAnalyser} from "./calendarAnalysis";
import {SingleValueHistoryDto} from "ecco-dto";
import {AddressHistoryDto, Individual} from "ecco-dto";
import {ConfigResolverDefault} from "ecco-dto";
import {getPercentComplete} from "ecco-dto";
import {ReferralSummaryDto, ServiceRecipientAssociatedContact, RelatedRelationship} from "ecco-dto";
import {fullAddress} from "ecco-dto";
import {
    agencyOnlyColumns,
    clientOnlyColumns,
    countsBy,
    getAgeInYearsBetweenDates,
    getMonth,
    individualOnlyColumns,
    professionalColumns,
    referralReportItemColumns,
    referralSummaryColumns
} from "../tables/predefined-table-representations";
import {
    groupByClientGroup,
    groupByCompany,
    groupByReferrer,
    groupByServiceGroup
} from "./referralCommonAnalysis";
import {EntityWithParent, NumberAnalysis, RelationshipWithRefToReferralAggregate} from "./types";
import {
    booleanColumn,
    columnMap,
    dateColumn,
    dateTimeColumnFromIsoUtc,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import {Agency} from "ecco-dto";
import {Client} from "ecco-dto";
import {Review} from "ecco-dto";
import {AnalysisContext} from "../chart-domain";

/** This deals with clicking on chart segment */
const WrapUnGroupReferralAggregateSequenceAnalyser: Analyser<
    Group<ReferralAggregate>,
    Sequence<ReferralAggregate>
> = function (ctx: AnalysisContext, input: Group<ReferralAggregate>): ReferralAggregateAnalysis {
    return new ReferralAggregateAnalysis(ctx, input.elements);
};

const WrapSingleReferralAggregateSequenceAnalyser: Analyser<
    ReferralAggregate,
    Sequence<ReferralAggregate>
> = function (ctx: AnalysisContext, input: ReferralAggregate): ReferralAggregateAnalysis {
    return new ReferralAggregateAnalysis(ctx, Lazy([input]));
};

//*********************************
// Analysis: GroupedReferralAnalysis

class GroupedReferralAnalysis extends SequenceAnalysis<Group<ReferralAggregate>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<ReferralAggregate>>) {
        super(ctx, data, (item: Group<ReferralAggregate>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapUnGroupReferralAggregateSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", ReferralAggregateAnalysis);
    }
}

//*********************************
// Analysers: GroupedReferralAnalysis

type ReferralToGroupedReferralAnalyser = Analyser<
    Sequence<ReferralAggregate>,
    Sequence<Group<ReferralAggregate>>
>;

function countsOfNumberOfReferrals(
    key: string,
    input: Sequence<ReferralAggregate>
): Group<ReferralAggregate> {
    return {
        key: key,
        count: input.size(),
        elements: input
    };
}

// TODO introduce this alterntiave referrralCountsBy which reduces the multiple groupFn's to a property on the referral
/*
function referralCountsBy(input: Sequence<ReferralAggregate>, keyFn: (dto: ReferralAggregate) => string): Sequence<Group<ReferralAggregate>> {
    return
        input.groupBy((inputElement) =>
            keyFn(inputElement))
        .pairs()
        .map( extractPair )

        .map((pair) => {
            var input: Sequence<ReferralAggregate> = pair.elements;
            return countsOfNumberOfReferrals(pair.key, input);
        });
}
*/
function referralCountsBy(
    input: Sequence<ReferralAggregate>,
    groupFn: GroupFn<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<Group<ReferralAggregate>> {
    return groupFn(input, ctx).map(pair => {
        const input: Sequence<ReferralAggregate> = pair.elements;
        return countsOfNumberOfReferrals(pair.key, input);
    });
}

class GroupedReferralAggregateAnalysis extends GroupedReferralAnalysis {
    constructor(ctx: AnalysisContext, data: Sequence<Group<ReferralAggregate>>) {
        super(ctx, data);
        this.derivativeAnalysers = {
            referralSummaryByGroup: referralSummaryByGroupAnalyser
        };
        this.recordRepresentation = {
            //
        };
        this.addOnClickAnalyser("ungroup", WrapUnGroupReferralAggregateSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", ReferralAggregateAnalysis);
    }
}

const referralCountsByAgeAtDateOfReferralAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByAgeAtDateOfReferral, ctx)
    );
};

const referralCountsByNumberOfChildReferralsAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, referralCommonAnalysis.groupByCountOfChildRecipients, ctx)
    );
};

const referralCountsByRegionAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, referralCommonAnalysis.groupByReferralRegionName, ctx)
    );
};
const referralCountsByProjectAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByReferralProjectName, ctx)
    );
};
const referralCountsByServiceAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByReferredService, ctx)
    );
};
const referralCountsByCompanyAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(ctx, referralCountsBy(input, groupByCompany, ctx));
};
const referralCountsByServiceGroupAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByServiceGroup, ctx)
    );
};
const referralCountsByClientGroupAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByClientGroup, ctx)
    );
};
const referralCountsByReferrerAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(ctx, referralCountsBy(input, groupByReferrer, ctx));
};
const referralCountsByEthnicityAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByEthnicity, ctx)
    );
};
const referralCountsByGenderAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(ctx, referralCountsBy(input, groupByGender, ctx));
};
const referralCountsByReligionAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(ctx, referralCountsBy(input, groupByReligion, ctx));
};
const referralCountsBySexualOrientationAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupBySexualOrientation, ctx)
    );
};
const referralCountsByFirstLanguageAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByFirstLanguage, ctx)
    );
};
const referralCountsByDisabilityAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByDisability, ctx)
    );
};
const referralCountsByWorkerAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByReferralAssignedWorker, ctx)
    );
};
const referralCountsByInterviewer1Analyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, referralCommonAnalysis.groupByReferralAssignedInterviewer1, ctx)
    );
};
const referralCountsByStatusAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByReferralStatus, ctx)
    );
};
const referralCountsByExitReasonAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByExitReason, ctx)
    );
};
const referralCountsByLatestClientStatusAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByLatestClientStatus, ctx)
    );
};
const referralCountsBySignpostReasonAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupBySignpostReason, ctx)
    );
};
const referralCountsByLengthOnServiceAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByLengthOnService, ctx)
    );
};
const referralCountsByLengthOnWaitingAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupByLengthOnWaiting, ctx)
    );
};
const referralsByClientCountAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCommonAnalysis.groupByReferralGroupCount(
            referralCountsBy(input, referralCommonAnalysis.groupByReferralClientId, ctx)
        )
    );
};


interface GroupReferralAggregateSummary extends Group<ReferralAggregate> {
    averageWaitingLength: number;
}
const referralSummaryByGroupAnalyser: Transformer<
    Group<ReferralAggregate>,
    GroupReferralAggregateSummary
> = function (
    ctx: AnalysisContext,
    input: Sequence<Group<ReferralAggregate>>
): SequenceAnalysis<GroupReferralAggregateSummary> {
    return new GroupReferralAggregateSummaryAnalysis(ctx, referralSummaryCalc(input, ctx));
};
function referralSummaryCalc(
    input: Sequence<Group<ReferralAggregate>>,
    ctx: AnalysisContext
): Sequence<GroupReferralAggregateSummary> {
    // we already receive a group, and it shouldn't be more than one group
    const dataOut = new GroupReferralAggregateSummaryAccumulator(ctx).reduce(
        input.first().elements
    );
    return Lazy(dataOut);
}

interface ReferralAggregateSummaryTotal {
    totalLength: number;
    totalItems: number;
}
class GroupReferralAggregateSummaryAccumulator
    implements Accumulator<ReferralAggregateSummaryTotal, ReferralAggregate>
{
    private readonly memo: ReferralAggregateSummaryTotal;

    constructor(private ctx: AnalysisContext) {
        this.memo = {
            totalLength: 0,
            totalItems: 0
        };
    }

    private accumulate(prev: ReferralAggregateSummaryTotal, next: ReferralAggregate) {
        const days = lengthOfDaysOnWaiting(next);
        const curr = {
            totalLength: prev.totalLength + (days || 0),
            totalItems: prev.totalItems + 1
        };
        return curr;
    }

    private postProcess(): void {}

    reduce(data: Sequence<ReferralAggregate>): ReferralAggregateSummaryTotal {
        const result = data.reduce((prev, curr) => this.accumulate(prev, curr), this.memo);
        this.postProcess();
        return result;
    }
}
/* example badge, see "dashboard referrals" showing badges with click through
{
    "description": "average days waiting",
        "stageType": "BADGE",
        "badgeRepresentation": {
        "badgeIconCssClasses": "fa fa-file",
            "recordRepresentationClassName": "GroupReferralAggregateSummaryOnlyColumns",
            "mainIndicatorValue": "average days waiting"
    }
},
*/
export class GroupReferralAggregateSummaryAnalysis extends SequenceAnalysis<GroupReferralAggregateSummary> {
    constructor(ctx: AnalysisContext, data: Sequence<GroupReferralAggregateSummary>) {
        super(ctx, data, item => item.key);
        this.recordRepresentation = {
            GroupReferralAggregateSummaryOnlyColumns: groupReferralAggregateSummaryOnlyColumns
        };
        this.addOnClickAnalyser("ungroup", "WrapUnGroupReferralAggregateSequenceAnalyser");
        //this.addOnClickManyAnalysis("ungroup", "ReferralAggregateAnalysis");
        //this.addOnClickAnalyser("single", WrapReferralAggregateAnalysisWithVisitsAnalyser);
    }
}
const groupReferralAggregateSummaryOnlyColumns = columnMap(
    numberColumn<GroupReferralAggregateSummary>("average waiting", row => row.averageWaitingLength)
);

/** This deals with clicking on table row */
const ReferralUngroupAnalyser: Analyser<
    Sequence<ReferralAggregate>,
    Sequence<ReferralAggregate>
> = function (ctx: AnalysisContext, input: Sequence<ReferralAggregate>): ReferralAggregateAnalysis {
    return new ReferralAggregateAnalysis(ctx, input);
};

//*********************************
// Analysers for referralsNotAcceptedWithin2WeeksAnalyser Filtering

function referralNotAcceptedWithin(weeks: number, ra: ReferralAggregate): boolean {
    // include null received date as will be useful to see - although perhaps better as an exception report
    // its still accurate to put in here
    if (ra.referral.receivedDate == null) {
        return true;
    }

    // When decisionMadeOn is null, it means we should compare the receivedDate to the report end date
    // and if there has been a <weeks> lapse, then it is overdue. However, we can't get the report end date easily,
    // but we could use 'today'.
    // Using today, the further in the past we go, the more likely a decisionMadeOn has been either:
    //      1 - set/provided, or
    //      2 - more likely overdue
    // However, the case where this doesn't work well is when a report end date is just before today.
    // Also it doesn't support 'what will be overdue in the future' without knowing the report end date.

    // So either we
    //  1 - fix the report without dates
    //  2 - use server side to get those overdue
    //  3 - send report parameters through to the analysis
    // we go for 1) for now so the below is accurate
    const reportAsAtDate = EccoDate.todayLocalTime();

    const end = ra.referral.decisionMadeOn
        ? EccoDate.parseIso8601(ra.referral.decisionMadeOn)
        : reportAsAtDate;
    const startWithWeeks = EccoDate.parseIso8601(ra.referral.receivedDate).addDays(7 * weeks);
    return startWithWeeks.compare(end) < 1;
}

function referralsNotAcceptedWithin(
    weeks: number,
    input: Sequence<ReferralAggregate>
): Sequence<ReferralAggregate> {
    return input.filter(ra => ra != null).filter(ra => referralNotAcceptedWithin(weeks, ra));
}

const referralsNotAcceptedWithin2WeeksAnalyser: Transformer<ReferralAggregate, ReferralAggregate> =
    function (
        ctx: AnalysisContext,
        input: Sequence<ReferralAggregate>
    ): SequenceAnalysis<ReferralAggregate> {
        return new ReferralAggregateAnalysis(ctx, referralsNotAcceptedWithin(2, input));
    };

//*********************************
// Analysers for referralsNotSignedDataProtectionAnalyser Filtering

function referralsNotSignedDataProtection(
    input: Sequence<ReferralAggregate>
): Sequence<ReferralAggregate> {
    return input
        .filter(ra => ra != null)
        .filter(ra => ra.referral != null)
        .filter(ra => !ra.referral.dataProtectionSignedId);
}

const referralsNotSignedDataProtectionAnalyser: Transformer<ReferralAggregate, ReferralAggregate> =
    function (
        ctx: AnalysisContext,
        input: Sequence<ReferralAggregate>
    ): SequenceAnalysis<ReferralAggregate> {
        return new ReferralAggregateAnalysis(ctx, referralsNotSignedDataProtection(input));
    };

//*********************************
// Analysers for referralsWithoutSupportWorkNotAcceptedWithin Filtering

function referralWithoutWorkNotAcceptedWithin(weeks: number, ra: ReferralAggregate): boolean {
    if (EccoDate.parseIso8601(ra.referral.decisionMadeOn!) == null) {
        return false;
    }

    // just as with referralAnalysis.referralNotAcceptedWithin - we need to use 'today' so we ensure the report
    // is 'as at' for now
    const reportAsAtDate = EccoDate.todayLocalTime();

    const end = reportAsAtDate;
    const startWithWeeks = EccoDate.parseIso8601(ra.referral.decisionMadeOn!).addDays(7 * weeks);
    return startWithWeeks.compare(end) < 1;
}

function referralsWithoutSupportWorkNotAcceptedWithin(
    weeks: number,
    input: Sequence<ReferralAggregate>
): Sequence<ReferralAggregate> {
    return (
        input
            .filter(ra => ra != null)
            // do we want to show those with support work but not done in 2 weeks?
            // we opt for no - otherwise we need to extract a firstWorkDate
            // and compare against the report end date which we don't have anyway
            // also - this is meant to be a 'overdue' report, which implies around now
            // so we reject those who have support work whenever that may have been done
            .filter(ra => ra.supportWork!.size() == 0)
            .filter(ra => referralWithoutWorkNotAcceptedWithin(weeks, ra))
    );
}

export var referralsWithoutSupportWorkNotAcceptedWithin2WeeksAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregate> {
    return new ReferralAggregateAnalysis(
        ctx,
        referralsWithoutSupportWorkNotAcceptedWithin(2, input)
    );
};

//*********************************
// Analysers for referralsWithoutRiskWorkNotAcceptedWithin Filtering

function referralsWithoutRiskWorkNotAcceptedWithin(
    weeks: number,
    input: Sequence<ReferralAggregate>
): Sequence<ReferralAggregate> {
    return input
        .filter(ra => ra != null)
        .filter(ra => ra.riskWork!.size() == 0)
        .filter(ra => referralWithoutWorkNotAcceptedWithin(weeks, ra));
}

export var referralsWithoutRiskWorkNotAcceptedWithin2WeeksAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregate> {
    return new ReferralAggregateAnalysis(ctx, referralsWithoutRiskWorkNotAcceptedWithin(2, input));
};

//*********************************
// Relationships

export var flattenToRelationshipsWithRefToAggregateAnalyser: Transformer<
    ReferralAggregate,
    RelationshipWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<RelationshipWithRefToReferralAggregate> {
    return new RelationshipWithParentRefAnalysis(ctx, flattenRelationships(input));
};

/** Produce a sequence of pairs of referral, work such that they can be grouped by entries relating to work */
function flattenRelationships(
    input: Sequence<ReferralAggregate>
): Sequence<RelationshipWithRefToReferralAggregate> {
    return input
        .filter(ra => ra != null && ra.relatedReferrals != null)
        .map(ra =>
            ra.relatedReferrals!.map(relation => ensureRelationshipReferencesItem(ra, relation))
        )
        .flatten<RelationshipWithRefToReferralAggregate>();
}

function ensureRelationshipReferencesItem(
    item: ReferralAggregate,
    relationship: RelatedRelationship
): RelationshipWithRefToReferralAggregate {
    const result = <RelationshipWithRefToReferralAggregate>relationship;
    result.reportItem = item;
    return result;
}

class RelationshipWithParentRefAnalysis extends SequenceAnalysis<RelationshipWithRefToReferralAggregate> {
    constructor(ctx: AnalysisContext, data: Sequence<RelationshipWithRefToReferralAggregate>) {
        super(ctx, data, (item: RelationshipWithRefToReferralAggregate) =>
            item.referralId.toString()
        );
        this.derivativeAnalysers = {
            relationshipCountsByRelation: relationshipCountsByRelationAnalyser
        };
        this.recordRepresentation = {
            Relationship: relationshipColumns
        };
    }
}

function getAgeInYearsToTimeOfNow(date: string): number | null {
    if (!date) {
        return null;
    }
    return getAgeInYearsBetweenDates(date, EccoDate.todayLocalTime().formatIso8601());
}

const relationshipOnlyColumns = columnMap(
    numberColumn<RelatedRelationship>("referralId", row => row.referralId),
    numberColumn<RelatedRelationship>("clientId", row => row.clientId),
    numberColumn<RelatedRelationship>("primaryReferralId", row => row.primaryReferralId),
    textColumn<RelatedRelationship>("relation", row => row.clientDisplayName),
    dateColumn<RelatedRelationship>("birthDate", row => EccoDate.parseIso8601(row.birthDate)),
    numberColumn<RelatedRelationship>("birthMonth", row => getMonth(row.birthDate)),
    numberColumn<RelatedRelationship>("age", row => getAgeInYearsToTimeOfNow(row.birthDate)),
    textColumn<RelatedRelationship>("relationship", row => row.relationship)
);
const relationshipToReferralAndClientColumns = joinNestedPathColumnMaps<
    RelationshipWithRefToReferralAggregate,
    ReferralAggregate
>("primary", row => row.reportItem, referralReportItemColumns); // referralReportItemColumns includes client information also (assumed to be requested in the report def)
const relationshipColumns = joinColumnMaps(
    relationshipOnlyColumns,
    relationshipToReferralAndClientColumns
);

//*********************************
// Relationships - Grouped

const relationshipCountsByRelationAnalyser: Transformer<
    RelationshipWithRefToReferralAggregate,
    RelationshipWithRefGroupWithCount
> = function (
    ctx: AnalysisContext,
    input: Sequence<RelationshipWithRefToReferralAggregate>
): SequenceAnalysis<RelationshipWithRefGroupWithCount> {
    return new GroupedRelationshipWithParentRefAnalysis(
        ctx,
        relationshipsCountsBy(input, groupByRelationship)
    );
};

function relationshipsCountsBy(
    input: Sequence<RelationshipWithRefToReferralAggregate>,
    groupFn: GroupFn<RelationshipWithRefToReferralAggregate>
): Sequence<RelationshipWithRefGroupWithCount> {
    return groupFn(input).map(pair => {
        const result: RelationshipWithRefGroupWithCount = {
            key: pair.key,
            elements: pair.elements,
            count: pair.elements.flatten().size()
        };
        return result;
    });
}

function groupByRelationship(
    input: Sequence<RelationshipWithRefToReferralAggregate>
): Sequence<Group<RelationshipWithRefToReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.relationship || "no relationship")
        .pairs()
        .map(extractPair);
}

interface RelationshipWithRefGroupWithCount extends Group<RelationshipWithRefToReferralAggregate> {
    count: number;
}

class GroupedRelationshipWithParentRefAnalysis extends GroupedAnalysis<
    RelationshipWithRefToReferralAggregate,
    RelationshipWithRefGroupWithCount
> {
    constructor(ctx: AnalysisContext, data: Sequence<RelationshipWithRefGroupWithCount>) {
        super(ctx, data);
        this.recordRepresentation = {
            RelationshipCount: relationshipCountColumns
        };
        // this.derivativeAnalysers = {
        //     "unGroupRelationship": unGroupWorkWithRefGroupWithCountAnalyser // WIP
        // };
        this.addOnClickAnalyser("ungroup", wrapRelationshipWithParentRefSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", RelationshipWithParentRefAnalysis);
    }
}

const relationshipCountColumns = columnMap(
    // choose one of these 'key' columns according to the analyser before this chart/table
    textColumn<RelationshipWithRefGroupWithCount>("relationship", row => row.key),
    numberColumn<RelationshipWithRefGroupWithCount>("count", row => row.count)
);
/** This deals with clicking on chart segment */
const wrapRelationshipWithParentRefSequenceAnalyser: Analyser<
    Group<RelationshipWithRefToReferralAggregate>,
    Sequence<RelationshipWithRefToReferralAggregate>
> = function (
    ctx: AnalysisContext,
    input: Group<RelationshipWithRefToReferralAggregate>
): RelationshipWithParentRefAnalysis {
    return new RelationshipWithParentRefAnalysis(ctx, input.elements);
};

//*********************************
// AssociatedContacts

// associatedContactsOnly shows the sr's 'contacts'
const associatedContactsBaseColumns = columnMap(
    numberColumn<ServiceRecipientAssociatedContact>("sr-id", row => row.serviceRecipientId),
    dateTimeColumnFromIsoUtc<ServiceRecipientAssociatedContact>("created", row => row.created),
    dateColumn<ServiceRecipientAssociatedContact>("archived", row =>
        EccoDate.parseIso8601(row.archived)
    ),
    textColumn<ServiceRecipientAssociatedContact>("type(s)", (row, ctx) =>
        row.associatedTypeIds
            ?.map(a => ctx.getSessionData().getListDefinitionEntryById(a)?.getDisplayName())
            .join(",")
    )
);

const associatedContactsToReferralSummaryColumns = joinNestedPathColumnMaps<
    ServiceRecipientAssociatedContact,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);
const associatedContactsToContactColumns = joinNestedPathColumnMaps<
    ServiceRecipientAssociatedContact,
    Individual
>("i", row => row.contact, individualOnlyColumns);
const associatedContactsToAgencyColumns = joinNestedPathColumnMaps<
    ServiceRecipientAssociatedContact,
    Agency
>("a", row => row.organisation, agencyOnlyColumns);

const associatedContactsOnlyColumns = joinColumnMaps(
    associatedContactsBaseColumns,
    associatedContactsToContactColumns,
    associatedContactsToAgencyColumns
);

const associatedContactsWithReferralSummaryColumns = joinColumnMaps(
    associatedContactsOnlyColumns,
    associatedContactsToReferralSummaryColumns
);

export class AssociatedContactsAnalysis extends SequenceAnalysis<ServiceRecipientAssociatedContact> {
    constructor(ctx: AnalysisContext, data: Sequence<ServiceRecipientAssociatedContact>) {
        super(
            ctx,
            data,
            (item: ServiceRecipientAssociatedContact) =>
                `${item.serviceRecipientId}:${item.created}`
        );
        this.derivativeAnalysers = {
            associatedContactsCountsByType: associatedContactsCountsByTypeAnalyser,
            associatedContactsProfessionals: associatedContactsProfessionalsAnalyser
        };
        this.recordRepresentation = {
            AssociatedContactsOnly: associatedContactsOnlyColumns,
            AssociatedContactsWithReferralSummary: associatedContactsWithReferralSummaryColumns
        };
    }
}

// flatten with ref to referralSummary (not ReferralAggregate) as probably all we need
export var flattenToAssociatedContactsWithRefSummaryAnalyser: Transformer<
    ReferralAggregate,
    ServiceRecipientAssociatedContact
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ServiceRecipientAssociatedContact> {
    return new AssociatedContactsAnalysis(ctx, flattenContacts(input));
};

function flattenContacts(
    input: Sequence<ReferralAggregate>
): Sequence<ServiceRecipientAssociatedContact> {
    const dummyContact: ServiceRecipientAssociatedContact = {
        serviceRecipientId: null,
        contactId: null,
        created: null,
        archived: null,
        associatedTypeIds: []
    };
    return input
        .filter(ra => ra != null)
        .map(ra => {
            const dummyC = {...dummyContact, serviceRecipientId: ra.referral.serviceRecipientId};
            const c = ra.contacts != null && ra.contacts.size() > 0 ? ra.contacts : Lazy([dummyC]);
            return c.map(data => {
                data.referralSummary = ra.referral;
                return data;
            });
        })
        .flatten<ServiceRecipientAssociatedContact>();
}

//*********************************
// Contacts - Grouped

// copied from taskAnalysis

interface AssociatedTypeWithRefContact {
    associatedTypeId: number | null;
    associatedContact: ServiceRecipientAssociatedContact;
}

function filterAssociatedContactsProfessionals(
    input: Sequence<ServiceRecipientAssociatedContact>
): Sequence<ServiceRecipientAssociatedContact> {
    return input.filter(c => !!c.organisation);
}

let associatedContactsProfessionalsAnalyser: Analyser<
    Sequence<ServiceRecipientAssociatedContact>,
    Sequence<ServiceRecipientAssociatedContact>
> = function (
    ctx: AnalysisContext,
    input: Sequence<ServiceRecipientAssociatedContact>
): SequenceAnalysis<ServiceRecipientAssociatedContact> {
    return new AssociatedContactsAnalysis(ctx, filterAssociatedContactsProfessionals(input));
};


let associatedContactsCountsByTypeAnalyser: Transformer<
    ServiceRecipientAssociatedContact,
    Group<AssociatedTypeWithRefContact>
> = function (
    ctx: AnalysisContext,
    input: Sequence<ServiceRecipientAssociatedContact>
): SequenceAnalysis<Group<AssociatedTypeWithRefContact>> {
    const toTypeWithRef = input
        .map(item => {
            //const c = (item.associatedTypeIds != null && ra.contacts.size() > 0) ? ra.contacts : Lazy([dummyContact]);
            return item.associatedTypeIds.map(t => {
                const r: AssociatedTypeWithRefContact = {
                    associatedTypeId: t,
                    associatedContact: item
                };
                return r;
            });
        })
        .flatten<AssociatedTypeWithRefContact>();
    const toNoTypeWithRef = input
        .filter(item => !item.associatedTypeIds || item.associatedTypeIds.length == 0)
        .map(t => {
            const r: AssociatedTypeWithRefContact = {
                associatedTypeId: null,
                associatedContact: t
            };
            return r;
        })
        .flatten<AssociatedTypeWithRefContact>();

    const allTypes = toTypeWithRef.concat(toNoTypeWithRef);
    return new GroupedAssociatedContactsAnalysis(
        ctx,
        countsBy(allTypes, item =>
            item.associatedTypeId == null
                ? "none"
                : ctx
                      .getSessionData()
                      .getListDefinitionEntryById(item.associatedTypeId)
                      .getDisplayName()
        )
    );
};

class GroupedAssociatedContactsAnalysis extends GroupedAnalysis<
    AssociatedTypeWithRefContact,
    Group<AssociatedTypeWithRefContact>
> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<AssociatedTypeWithRefContact>>) {
        super(ctx, data);
        //this.recordRepresentation = {
        //    "GroupTaskStatusColumns": groupTaskStatusColumns
        //};
        // this.derivativeAnalysers = {
        //     "unGroupRelationship": unGroupWorkWithRefGroupWithCountAnalyser // WIP
        // };
        this.addOnClickAnalyser("ungroup", wrapAssociatedContactsAnalyser);
        this.addOnClickManyAnalysis("ungroup", AssociatedContactsAnalysis);
    }
}
let wrapAssociatedContactsAnalyser: Analyser<
    Group<AssociatedTypeWithRefContact>,
    Sequence<ServiceRecipientAssociatedContact>
> = function (
    ctx: AnalysisContext,
    input: Group<AssociatedTypeWithRefContact>
): AssociatedContactsAnalysis {
    const contacts = input.elements.map(item => item.associatedContact);
    return new AssociatedContactsAnalysis(ctx, contacts);
};

//*********************************
// SingleValueHistoryWithRefToReferralAggregate

const referralCountsBySupportLevelAnalyser: ReferralToGroupedReferralAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): GroupedReferralAnalysis {
    return new GroupedReferralAggregateAnalysis(
        ctx,
        referralCountsBy(input, groupReferralAggregateBySvhValue, ctx)
    );
};

function groupReferralAggregateBySvhValue(
    input: Sequence<ReferralAggregate>,
    ctx?: AnalysisContext
): Sequence<Group<ReferralAggregate>> {
    return constructGroupReferralAggregateBySvhValue(
        groupSvhWithReferralAggregateBySvhValue(svhWithReferralAggregate(input)),
        ctx
    );
}

// NB we would like to further filter here by svh that is applicable for the report dates
// BUT we don't have access to the report dates (or to know its criteria) so we need
// to place the logic in the ReportDataSourceFactory - which is less than ideal
function svhWithReferralAggregate(
    input: Sequence<ReferralAggregate>
): Sequence<{svh: SingleValueHistoryDto; ref: ReferralAggregate}> {
    return input
        .map(ra => {
            return ra.singleValueHistory!.map(svh => {
                return {svh: svh, ref: ra};
            });
        })
        .flatten<{svh: SingleValueHistoryDto; ref: ReferralAggregate}>();
}

function groupSvhWithReferralAggregateBySvhValue(
    input: Sequence<{svh: SingleValueHistoryDto; ref: ReferralAggregate}>
): Sequence<Group<{svh: SingleValueHistoryDto; ref: ReferralAggregate}>> {
    return (
        input
            .groupBy(svhWithReferralAggregate =>
                // the 'key' is the id of the value (the list def id) with the date applicable
                // this is so that we group and filter support on key:date combinations - not just key,
                // which might otherwise exclude support work because the 'first' key date found is not applicable
                svhWithReferralAggregate.svh.value
                    ? svhWithReferralAggregate.svh.value.toString()
                    : ""
            )
            // so far this will be the 'value' with an array of {svh: SingleValueHistoryDto; ref: ReferralAggregate}
            .pairs()
            // pairs moves this structure into an array of ['value', {svh: SingleValueHistoryDto; ref: ReferralAggregate}[]]
            .map(types.extractPair)
        // turns the pairs array into a Group {key: array[0], elements: array[1]}
        // which is key: 'value', elements: {svh: SingleValueHistoryDto; ref: ReferralAggregate}
    );
}

function constructGroupReferralAggregateBySvhValue(
    input: Sequence<Group<{svh: SingleValueHistoryDto; ref: ReferralAggregate}>>,
    ctx?: AnalysisContext
): Sequence<Group<ReferralAggregate>> {
    // get the first sessionData
    const sessionData = ctx!.getSessionData();

    // return the group -and transform the key to a recognisable value
    return input.map(input => {
        return {
            key: input.key
                ? sessionData.getListDefinitionEntryById(parseInt(input.key)).getName()
                : "no value",
            elements: input.elements
                .map(svhWithReferralAggregate => svhWithReferralAggregate.ref)
                .flatten<ReferralAggregate>()
        };
    });
}

//*********************************
// Analysers for referralToClientId Filtering

const referralToClientIdAnalyser: Analyser<
    Sequence<ReferralAggregate>,
    Sequence<number>
> = function (ctx: AnalysisContext, input: Sequence<ReferralAggregate>): SequenceAnalysis<number> {
    return new NumberAnalysis(ctx, uniqueReferralToClientId(input));
};

export function uniqueReferralToClientId(input: Sequence<ReferralAggregate>): Sequence<number> {
    return input.map(ra => ra.referral.clientId).uniq();
}

//*********************************
// Analysis: ClientAnalysis

export class ClientAnalysis extends SequenceAnalysis<Client> {
    constructor(ctx: AnalysisContext, data: Sequence<Client>) {
        super(ctx, data, (item: Client) => item.clientId!.toString());
        this.derivativeAnalysers = {
            clientToClientId: clientToClientIdAnalyser
        };
        this.recordRepresentation = {
            ClientOnly: clientOnlyColumns
        };
    }
}

const clientToClientIdAnalyser: Analyser<Sequence<Client>, Sequence<number>> = function (
    ctx: AnalysisContext,
    input: Sequence<Client>
): SequenceAnalysis<number> {
    return new types.NumberAnalysis(ctx, uniqueClientId(input));
};

export function uniqueClientId(input: Sequence<Client>): Sequence<number> {
    return input.map(client => client.clientId!).uniq();
}

//*********************************
// Analysis: Agency

export class AgencyAnalysis extends SequenceAnalysis<Agency> {
    constructor(ctx: AnalysisContext, data: Sequence<Agency>) {
        super(ctx, data, (item: Agency) => item.contactId!.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            AgencyOnly: agencyOnlyColumns
        };
    }
}

export class ProfessionalAnalysis extends SequenceAnalysis<Individual> {
    constructor(ctx: AnalysisContext, data: Sequence<Individual>) {
        super(ctx, data, (item: Individual) => item.contactId.toString());
        this.derivativeAnalysers = {
            // could extract just professionals
        };
        this.recordRepresentation = {
            ProfessionalOnly: professionalColumns
        };
    }
}

//*********************************
// Grouped Analysis: Review

const reviewCountsByCompleteAnalyser: Analyser<
    Sequence<Review>,
    Sequence<Group<Review>>
> = function (ctx: AnalysisContext, input: Sequence<Review>): GroupedReviewAnalysis {
    return new GroupedReviewAnalysis(ctx, reviewCountsBy(input, groupByReviewComplete));
};

export function groupByReviewComplete(input: Sequence<Review>): Sequence<Group<Review>> {
    return input
        .groupBy(inputElement => (inputElement.complete ? "complete" : "incomplete"))
        .pairs()
        .map(extractPair);
}

class GroupedReviewAnalysis extends SequenceAnalysis<Group<Review>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<Review>>) {
        super(ctx, data, (item: Group<Review>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapReviewSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", ReviewAnalysis);
    }
}

/** This deals with clicking on chart segment */
const WrapReviewSequenceAnalyser: Analyser<Group<Review>, Sequence<Review>> = function (
    ctx: AnalysisContext,
    input: Group<Review>
): ReviewAnalysis {
    return new ReviewAnalysis(ctx, input.elements);
};

function reviewCountsBy(
    input: Sequence<Review>,
    groupFn: GroupFn<Review>
): Sequence<Group<Review>> {
    return groupFn(input).map(pair => {
        let input: Sequence<Review> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}

//*********************************
// Analysis: Review

const reviewOnlyColumns = columnMap(
    numberColumn<Review>("rev-id", row => row.reviewId),
    dateColumn<Review>("review date", row => EccoDate.parseIso8601(row.startDate)),
    numberColumn<Review>("current page", row => row.reviewPage),
    booleanColumn<Review>("complete", row => row.complete),
    numberColumn<Review>("% complete", (row, ctx) => {
        const configResolver = ConfigResolverDefault.fromServiceRecipient(
            ctx.getSessionData(),
            row.referralSummary!.serviceAllocationId,
            null
        );
        const totalOutcomes = configResolver.getOutcomesFilteredForTask(
            "needsAssessmentReductionReview"
        ).length;
        return getPercentComplete(totalOutcomes, row.reviewPage);
    })
);

const reviewToReferralSummaryColumns = joinNestedPathColumnMaps<Review, ReferralSummaryDto>(
    "r",
    row => row.referralSummary,
    referralSummaryColumns
);

const reviewWithReferralSummaryColumns = joinColumnMaps(
    reviewOnlyColumns,
    reviewToReferralSummaryColumns
);

export class ReviewAnalysis extends SequenceAnalysis<Review> {
    constructor(ctx: AnalysisContext, data: Sequence<Review>) {
        super(ctx, data, (item: Review) => item.reviewId.toString());
        this.recordRepresentation = {
            ReviewOnly: reviewOnlyColumns,
            ReviewWithReferralSummary: reviewWithReferralSummaryColumns
        };
        this.derivativeAnalysers = {
            reviewCountsByComplete: reviewCountsByCompleteAnalyser
        };
    }
}

//*********************************
// Analysis: AddressHistory

const addressHistoryOnlyColumns = columnMap(
    numberColumn<AddressHistoryDto>("id", row => row.id),
    numberColumn<AddressHistoryDto>("address-id", row => row.addressId),
    numberColumn<AddressHistoryDto>("sr-id", row => row.serviceRecipientId),
    dateColumn<AddressHistoryDto>("valid from", row =>
        EccoDate.parseIso8601FromDateTime(row.validFrom)
    ),
    dateColumn<AddressHistoryDto>("valid to", row =>
        EccoDate.parseIso8601FromDateTime(row.validTo!)
    ),
    textColumn<AddressHistoryDto>("full address", row => fullAddress(row.address))
);

const addressHistoryToReferralSummaryColumns = joinNestedPathColumnMaps<
    AddressHistoryDto,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);

const addressHistoryWithReferralSummaryColumns = joinColumnMaps(
    addressHistoryOnlyColumns,
    addressHistoryToReferralSummaryColumns
);

export class AddressHistoryAnalysis extends SequenceAnalysis<AddressHistoryDto> {
    constructor(ctx: AnalysisContext, data: Sequence<AddressHistoryDto>) {
        super(ctx, data, (item: AddressHistoryDto) => item.id.toString());
        this.recordRepresentation = {
            AddressHistoryOnly: addressHistoryOnlyColumns,
            AddressHistoryWithReferralSummary: addressHistoryWithReferralSummaryColumns
        };
        this.derivativeAnalysers = {};
    }
}

//*********************************
// Analysis: ReferralSummaryAnalysis

export class ReferralSummaryAnalysis<P> extends SequenceAnalysis<
    EntityWithParent<P, ReferralSummaryDto>
> {
    constructor(ctx: AnalysisContext, data: Sequence<EntityWithParent<P, ReferralSummaryDto>>) {
        super(ctx, data, (item: ReferralSummaryDto) => item.referralId!.toString());
        this.derivativeAnalysers = {
            countsByServiceName: groupByServiceNameAnalyser
        };
        this.recordRepresentation = {
            ReferralSummaryCols: tableRepresentations.referralSummaryColumns
        };
    }
}
// avoid typing too much with const
const groupByServiceNameAnalyser = function (
    ctx: AnalysisContext,
    input: Sequence<EntityWithParent<any, ReferralSummaryDto>>
) {
    return new GroupedParentAnalysis(
        ctx,
        countsBy(
            input,
            i =>
                ctx.getSessionData().getServiceCategorisation(i.serviceAllocationId).serviceName ||
                "no service assigned"
        ),
        arg => arg.key
    );
};
/*
    // NB typed version
    type GroupedParentAnalyser<P> = Analyser<Sequence<EntityWithParent<P, ReferralSummaryDto>>, Sequence<Group<EntityWithParent<P, ReferralSummaryDto>>>>;
    //  'any' below is QuestionnaireAnswersSnapshotDto, but that struggles
    type GroupedParentQuestionnaireAnalyser = GroupedParentAnalyser<any>;
    const groupByServiceNameAnalyser: GroupedParentQuestionnaireAnalyser = function (ctx: AnalysisContext, input: Sequence<EntityWithParent<QuestionnaireAnswersSnapshotDto, ReferralSummaryDto>>) {
        return new GroupedParentAnalysis(ctx, countsBy(input, i => i.referredServiceName || "no service assigned"), (arg) => arg.key);
};
*/
class GroupedParentAnalysis<P, C> extends SequenceAnalysis<Group<EntityWithParent<P, C>>> {
    constructor(
        ctx: AnalysisContext,
        data: Sequence<Group<EntityWithParent<P, C>>>,
        keyFn: (arg: Group<EntityWithParent<P, C>>) => string
    ) {
        super(ctx, data, keyFn);
        this.derivativeAnalysers = {};
        this.recordRepresentation = {};
        this.addOnClickAnalyser("ungroup", "WrapUnGroupToQuestionnaireAnalyser");
        //this.addOnClickManyAnalysis("ungroup", ReferralAggregateAnalysis);
    }
}

//*********************************
// Analysis: ReferralAggregateAnalysis

export class ReferralAggregateAnalysis extends SequenceAnalysis<ReferralAggregate> {
    constructor(ctx: AnalysisContext, data: Sequence<ReferralAggregate>) {
        super(ctx, data, (item: ReferralAggregate) => item.referral.referralId.toString());
        this.derivativeAnalysers = {
            referralCountsByAgeAtDateOfReferral: referralCountsByAgeAtDateOfReferralAnalyser,
            referralCountsByNumberOfChildReferrals: referralCountsByNumberOfChildReferralsAnalyser,
            referralCountsByRegion: referralCountsByRegionAnalyser,
            referralCountsByProject: referralCountsByProjectAnalyser,
            referralCountsByService: referralCountsByServiceAnalyser,
            referralCountsByCompany: referralCountsByCompanyAnalyser,
            referralCountsByServiceGroup: referralCountsByServiceGroupAnalyser,
            referralCountsByClientGroup: referralCountsByClientGroupAnalyser,
            referralCountsByReferrer: referralCountsByReferrerAnalyser,
            referralCountsByWorker: referralCountsByWorkerAnalyser,
            referralCountsByInterviewer1: referralCountsByInterviewer1Analyser,
            referralCountsByStatus: referralCountsByStatusAnalyser,
            referralCountsByExitReason: referralCountsByExitReasonAnalyser,
            referralCountsByLatestClientStatus: referralCountsByLatestClientStatusAnalyser,
            referralCountsBySignpostReason: referralCountsBySignpostReasonAnalyser,
            referralCountsByEthnicity: referralCountsByEthnicityAnalyser,
            referralCountsByGender: referralCountsByGenderAnalyser,
            referralCountsByReligion: referralCountsByReligionAnalyser,
            referralCountsBySexualOrientation: referralCountsBySexualOrientationAnalyser,
            referralCountsByFirstLanguage: referralCountsByFirstLanguageAnalyser,
            referralCountsByDisability: referralCountsByDisabilityAnalyser,
            referralCountsByLengthOnService: referralCountsByLengthOnServiceAnalyser,
            referralCountsByLengthOnWaiting: referralCountsByLengthOnWaitingAnalyser,
            referralCountsBySupportLevel: referralCountsBySupportLevelAnalyser,
            referralsByClientCount: referralsByClientCountAnalyser,
            calendarEventsFromReferrals: calendarEventsFromReferralAggregateAnalyser,
            referralsNotAcceptedWithin2Weeks: referralsNotAcceptedWithin2WeeksAnalyser,
            referralsWithoutWorkNotAcceptedWithin2Weeks:
                referralsWithoutSupportWorkNotAcceptedWithin2WeeksAnalyser,
            referralsWithoutRiskWorkNotAcceptedWithin2Weeks:
                referralsWithoutRiskWorkNotAcceptedWithin2WeeksAnalyser,
            referralsNotSignedDataProtection: referralsNotSignedDataProtectionAnalyser,
            smartStepCountsByWorker: smartStepCountsAnalysis.smartStepCountsByWorkerAnalyser,
            smartStepCountsByAgeAtReferralDate:
                smartStepCountsAnalysis.smartStepCountsByAgeAtReferralAnalyser,
            smartStepCountsByProject: smartStepCountsAnalysis.smartStepCountsByProjectAnalyser,
            smartStepCountsByService: smartStepCountsAnalysis.smartStepCountsByServiceAnalyser,
            smartStepCountsByStatus: smartStepCountsAnalysis.smartStepCountsByStatusAnalyser,
            visitCountsByAuthor: visitAnalysis.visitSupportCountsByAuthorAnalyser,
            visitCountsByClient: visitAnalysis.visitSupportCountsByClientAnalyser,
            visitCountsByService: visitAnalysis.visitSupportCountsByServiceAnalyser,
            visitCountsByProject: visitAnalysis.visitSupportCountsByProjectAnalyser,
            visitCountsByWorker: visitAnalysis.visitSupportCountsByWorkerAnalyser,
            visitCountsByCommentType: visitAnalysis.visitSupportCountsByCommentTypeAnalyser,
            visitCountsBySupportLevel: visitAnalysis.visitSupportCountsBySupportLevelAnalyser,
            // visitAll is support and work
            visitAllCountsByService: visitAnalysis.visitAllCountsByServiceAnalyser,
            visitAllCountsByProject: visitAnalysis.visitAllCountsByProjectAnalyser,
            visitAllCountsByAuthor: visitAnalysis.visitAllCountsByAuthorAnalyser,
            visitAllCountsByAssignedWorker: visitAnalysis.visitAllCountsByAssignedWorkerAnalyser,
            activitiesByProject: activityAnalysis.activityCountsByProjectAnalyser,
            activitiesByActivityInterest: activityAnalysis.activityCountsByActivityInterestAnalyser,
            contactsFromReferrals: flattenToAssociatedContactsWithRefSummaryAnalyser,
            actionDefFromReferrals:
                actionDefAnalysis.actionDef_withRefTo_ReferralAggregate_Analyser,
            relationshipsFromReferrals: flattenToRelationshipsWithRefToAggregateAnalyser,
            smartStepFromReferrals:
                smartStepAnalysis.supportAction_withRefTo_ReferralAggregate_Analyser,
            workFromReferrals: workAnalysis.flattenToSupportWorkWithRefToAggregateAnalyser,
            customFormWorkFromReferrals:
                workAnalysis.flattenToCustomFormWorkWithRefToAggregateAnalyser,
            workAllFromReferrals: workAnalysis.flattenToAllWorkWithRefToAggregateAnalyser,
            qnAnswerWorkFromReferralsAnalyser:
                questionnaireAnalysis.qnAnswerWorkFromReferralAggregateAnalyser,
            // TODO split the below into 2 chained analysers: workFromReferrals and workByCommentType
            //  also this does not work in 'select all'
            workFromReferrals_JOIN_workByCommentType:
                workAnalysis.supportWorkFromReferrals_JOIN_workByCommentTypeAnalyser,
            hactSocialValue: hactAnalysis.hactSocialValueAnalyser,
            hactManagement: hactAnalysis.hactManagementAnalyser,
            referralToClientId: referralToClientIdAnalyser
        };
        this.recordRepresentation = {
            ReferralReportItem: tableRepresentations.referralReportItemColumns,
            ReferralWithReferrerAgencyReportItem:
                tableRepresentations.referralWithReferrerAgencyReportItemColumns
            // referrals with custom form details, but referralReportItemColumns already provides 'f:'
            // or we could add here "ReferralWithCustomForm":
        };
        this.addOnClickAnalyser("single", WrapSingleReferralAggregateSequenceAnalyser);
        //        this.addOnClickAnalyser("some single analyser", SingleReferralAnalyser);
    }

    override getKey(item: ReferralAggregate) {
        return item.referral.referralId.toString();
    }
}

types.analysersByName["WrapUnGroupReferralAggregateSequenceAnalyser"] =
    WrapUnGroupReferralAggregateSequenceAnalyser; // this extracts the group.elements
types.analysersByName["WrapSingleReferralAggregateSequenceAnalyser"] =
    WrapSingleReferralAggregateSequenceAnalyser;
types.analysisFactoriesByName["ReferralAggregateAnalysis"] = ReferralAggregateAnalysis;
