///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import types = require("./types");
import tableRepresentations = require("../tables/predefined-table-representations");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Analyser = types.Analyser;
import Group = types.Group;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import extractPair = types.extractPair;
import GroupFn = types.GroupFn;
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";

import {BaseWork, SupportWork} from "ecco-dto";
import {SessionData, HactSessionData} from "ecco-dto";
import {QuestionnaireWorkDto} from "ecco-dto";
import {
    ManagementIntermediateData,
    ManagementSummaryData,
    SocialValueIntermediateData,
    SocialValueSummaryDataFull
} from "ecco-dto";
import {Client} from "ecco-dto";
import {HactClientCompositeDataFull} from "ecco-dto";
import {AnalysisContext} from "../chart-domain";
import {
    booleanColumn,
    columnMap,
    dateTimeColumn,
    numberColumn,
    textColumn
} from "../controls/tableSupport";

// ***************************
// HACT MANAGEMENT
// ***************************

var hactManagementColumns = columnMap(
    textColumn<HactManagementData>("c-id", row => row.clientCode),
    textColumn<HactManagementData>("client name", row => row.clientDisplayName),
    numberColumn<HactManagementData>("questionId", row => row.questionDefId),
    textColumn<HactManagementData>("questionName", row => row.questionName),
    dateTimeColumn<HactManagementData>(
        "pre survey support date",
        row => row.preSurveySupportWorkDate || null
    ),
    textColumn<HactManagementData>("survey name", row => row.surveyName),
    textColumn<HactManagementData>("survey status", row => row.surveyStatus),
    dateTimeColumn<HactManagementData>(
        "survey answer date",
        row => row.surveyAnswerWorkDate || null
    ),
    booleanColumn<HactManagementData>("survey valuable change", row => row.valuableChange),
    numberColumn<HactManagementData>("survey days expire", row => row.surveyDaysUntilExpire)
);

export class HactManagementDataAnalysis extends SequenceAnalysis<HactManagementData> {
    constructor(ctx: AnalysisContext, data: Sequence<HactManagementData>) {
        super(ctx, data, (item: HactManagementData) => item.clientCode);
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            hactManagement: hactManagementColumns
        };
    }
}

export interface HactManagementData extends ManagementIntermediateData {
    clientCode: string;
    clientDisplayName: string;
    questionName: string;
    valuableChange: boolean;
}

/**
 * Takes a series of referrals and works out the social value calculation.
 * NB should include all services, as hact answers can be recorded across referrals
 */
export var hactManagementAnalyser: Transformer<ReferralAggregate, HactManagementData> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<HactManagementData> {
    return new HactManagementDataAnalysis(ctx, hactManagementValues(groupByClientId(input)));
};

function hactManagementValues(
    input: Sequence<Group<ReferralAggregate>>
): Sequence<HactManagementData> {
    return (
        input
            .map(pair => {
                const clientId = parseInt(pair.key);
                const clientCode = pair.elements.first().referral.clientCode || clientId.toString();
                const clientDisplayName = pair.elements.first().referral.displayName || "";
                const sessionData = pair.elements.first().sessionData!;
                const hactSessionData = pair.elements.first().hactSessionData!;
                const reportCriteria = pair.elements.first().reportCriteria!;

                // NOT IDEAL
                // using a referral-based report (not a client-based one) means the server
                // side ordering is circumvented - it should be workDate desc, created desc
                // see QuestionnaireEvidenceController.findByClientIdAndEvidenceGroupKey
                const clientQuestionnaireWork = pair.elements
                    .map(ra => ra.questionnaireWork)
                    .flatten<QuestionnaireWorkDto>()
                    .sort((a, b) => sortClientWork(a, b));
                // NOT IDEAL

                // NOT IDEAL
                // using a referral-based report (not a client-based one) means the server
                // side ordering is circumvented - it should be workDate desc, created desc
                let clientSupportWork = pair.elements
                    .map(ra => ra.supportWork)
                    .flatten<SupportWork>()
                    .sort((a, b) => {
                        return sortClientWork(a, b);
                    });
                // NOT IDEAL

                let toDte = EccoDate.parseIso8601(reportCriteria.to)!;

                // transforms the client's answers (from the built HactAnswerHistorySummary)
                // into a question based array for the client
                // NB this method has broken the <Sequence>
                // TODO work query should be limited to hactOnly
                let hactClientCompositeData = new HactClientCompositeDataFull(
                    hactSessionData,
                    clientId,
                    clientQuestionnaireWork.toArray(),
                    clientSupportWork.toArray(),
                    []
                );

                let mgtInt = new ManagementSummaryData(hactClientCompositeData, toDte);

                // return an intermediate step - useful in debugging the clobbered <Sequence>
                return {
                    summary: mgtInt,
                    clientCode: clientCode,
                    clientDisplayName: clientDisplayName,
                    sessionData: sessionData,
                    hactSessionData: hactSessionData
                };
            })

            // we're only interested in clients with something to show
            .filter(data => data.summary.getManagementIntermediateForClient().length > 0)

            // add 'valuable change' indication per question
            .map(data => {
                // NB groupBy seems to imply creating an interface/class
                const uniqueQns = Array.from(
                    new Set(
                        data.summary
                            .getManagementIntermediateForClient()
                            .map(item => item.questionDefId)
                    )
                );
                return uniqueQns.map(qnId => {
                    // a valuable change depends on when the reporting period is taken, and we don't have that information
                    // so the simplest and cleanest thing so far (also taking year 2 considerations into account) is to
                    // indicate a rolling change from the last answer.

                    const preSurvey = <HactManagementData>(
                        data.summary.getManagementIntermediateForClient(qnId)[0]
                    );
                    const postSurvey1 = <HactManagementData>(
                        data.summary.getManagementIntermediateForClient(qnId)[1]
                    );
                    const postSurvey2 = <HactManagementData>(
                        data.summary.getManagementIntermediateForClient(qnId)[2]
                    );

                    const preSurveyAnswer = preSurvey.surveyAnswerValue;
                    const postSurvey1Answer = postSurvey1.surveyAnswerValue;
                    const postSurvey2Answer = postSurvey2.surveyAnswerValue;

                    if (preSurveyAnswer) {
                        if (postSurvey1Answer) {
                            // snippet taken from createSocialValueForQuestion
                            const answerChoiceDefId1 = data.sessionData.getAnswerValueId(
                                qnId,
                                preSurveyAnswer
                            )!;
                            const answerChoiceDefId2 = data.sessionData.getAnswerValueId(
                                qnId,
                                postSurvey1Answer
                            )!;
                            const valuableHactOutcome =
                                data.hactSessionData.getValuableChangeOutcome(
                                    qnId,
                                    answerChoiceDefId1,
                                    answerChoiceDefId2
                                );
                            postSurvey1.valuableChange = !!valuableHactOutcome;
                        }
                        if (postSurvey2Answer) {
                            // use the rolling changes, not the pre value - as above describes
                            const answerChoiceDefId1 = data.sessionData.getAnswerValueId(
                                qnId,
                                postSurvey1Answer ? postSurvey1Answer : preSurveyAnswer
                            )!;
                            const answerChoiceDefId2 = data.sessionData.getAnswerValueId(
                                qnId,
                                postSurvey2Answer
                            )!;
                            const valuableHactOutcome =
                                data.hactSessionData.getValuableChangeOutcome(
                                    qnId,
                                    answerChoiceDefId1,
                                    answerChoiceDefId2
                                );
                            postSurvey2.valuableChange = !!valuableHactOutcome;
                        }
                    }

                    return {
                        qnDefId: qnId,
                        summaryPerQn: [preSurvey, postSurvey1, postSurvey2],
                        clientCode: data.clientCode,
                        clientDisplayName: data.clientDisplayName,
                        sessionData: data.sessionData,
                        hactSessionData: data.hactSessionData
                    };
                });
            })

            // add the client code and question name to each row
            .flatten<{
                qnDefId: number;
                summaryPerQn: HactManagementData[];
                clientCode: string;
                clientDisplayName: string;
                sessionData: SessionData;
                hactSessionData: HactSessionData;
            }>()
            .map(data => {
                return data.summaryPerQn.map(mgtPerQn => {
                    mgtPerQn.clientCode = data.clientCode;
                    mgtPerQn.clientDisplayName = data.clientDisplayName;
                    mgtPerQn.questionName = data.hactSessionData.getQuestion(
                        mgtPerQn.questionDefId
                    ).name;
                    return mgtPerQn;
                });
            })

            .flatten<HactManagementData>()
    );
}

// ***************************
// HACT SOCIAL VALUE
// ***************************

// *********
// ANALYSERS

var hactSocialValueTotalsByAgeAnalyser: Analyser<
    Sequence<HactSocialValue>,
    Sequence<Group<HactSocialValue>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<HactSocialValue>
): HactSocialValueGroupedAnalysis {
    return new HactSocialValueGroupedAnalysis(
        ctx,
        hactSocialValueTotalsBy(input, hact_groupByAgeAtDateOfReferral)
    );
};
function hactSocialValueTotalsBy(
    input: Sequence<HactSocialValue>,
    groupFn: GroupFn<HactSocialValue>
): Sequence<Group<HactSocialValue>> {
    return groupFn(input).map(pair => {
        var input: Sequence<HactSocialValue> = pair.elements;
        return hactSocialValueTotalOf(pair.key, input);
    });
}
// we turn the count into a total value be returning the total but also naming the chart
// eg "seriesDefs": [{ "label": "value", "valuePath": "count", "renderMode": "PIE" }]
function hactSocialValueTotalOf(
    key: string,
    input: Sequence<HactSocialValue>
): Group<HactSocialValue> {
    let totalValue = input
        .map(hsv => hsv.valuableHactValue)
        .reduce((prev, curr) => {
            return prev + curr;
        });
    return {
        key: key,
        count: totalValue,
        elements: input
    };
}
function hact_groupByAgeAtDateOfReferral(
    input: Sequence<HactSocialValue>
): Sequence<Group<HactSocialValue>> {
    return input
        .groupBy(inputElement =>
            getHactQuantisedAgeInYearsAtValuableChange(inputElement.clientAgeAtValuableAnswer!)
        )
        .pairs()
        .map(extractPair);
}
function getHactQuantisedAgeInYearsAtValuableChange(value: number): string {
    if (value < 25) {
        return "< 25";
    }
    if (value < 50) {
        return "25-50";
    }
    return "50 and over";
}

class HactSocialValueGroupedAnalysis extends SequenceAnalysis<Group<HactSocialValue>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<HactSocialValue>>) {
        super(ctx, data, (item: Group<HactSocialValue>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapHactSocialValueSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", HactSocialValueAnalysis);
    }
}

/** This deals with clicking on chart segment */
var WrapHactSocialValueSequenceAnalyser: Analyser<
    Group<HactSocialValue>,
    Sequence<HactSocialValue>
> = function (ctx: AnalysisContext, input: Group<HactSocialValue>): HactSocialValueAnalysis {
    return new HactSocialValueAnalysis(ctx, input.elements);
};

// *********
// TABLE
var hactSocialValueColumns = columnMap(
    textColumn<HactSocialValue>("c-id", row => row.clientCode),
    textColumn<HactSocialValue>("client name", row => row.clientDisplayName),
    numberColumn<HactSocialValue>("age at answer", row => row.clientAgeAtValuableAnswer, true),
    textColumn<HactSocialValue>("valuable", row => (row.valuableChange ? "yes" : "no")),
    numberColumn<HactSocialValue>("questionId", row => row.questionDefId),
    textColumn<HactSocialValue>("questionName", row => row.questionName),
    textColumn<HactSocialValue>("valuableHactOutcome", row => row.valuableHactOutcomeDefCode), // see 'we can't currently know the hact outcome for the question unless its valuable'
    numberColumn<HactSocialValue>("valuableHactValue", row => row.valuableHactValue),
    dateTimeColumn<HactSocialValue>("workDate1", row => row.workDate1),
    dateTimeColumn<HactSocialValue>("workDate2", row => row.workDate2),
    numberColumn<HactSocialValue>("answerChoiceDefId1", row => row.answerChoiceDefId1),
    numberColumn<HactSocialValue>("answerChoiceDefId2", row => row.answerChoiceDefId2),
    textColumn<HactSocialValue>("answer1", row => row.answerChoiceStr1),
    textColumn<HactSocialValue>("answer2", row => row.answerChoiceStr2)
);

export class HactSocialValueAnalysis extends SequenceAnalysis<HactSocialValue> {
    constructor(ctx: AnalysisContext, data: Sequence<HactSocialValue>) {
        super(ctx, data, (item: HactSocialValue) => item.clientCode);
        this.derivativeAnalysers = {
            hactSocialValueTotalsByAge: hactSocialValueTotalsByAgeAnalyser
        };
        this.recordRepresentation = {
            hactSocialValue: hactSocialValueColumns
        };
    }
}

// social value based on a client
export interface HactSocialValue {
    clientCode: string;
    clientDisplayName: string; // one field when using the referral (which we don't pass through)
    client: Client;
    clientAgeAtValuableAnswer: number | null;
    questionDefId: number;
    questionName: string;
    answerChoiceDefId1: number;
    answerChoiceDefId2: number;
    valuableChange: boolean;
    valuableHactOutcomeDefCode: string;
    valuableHactValue: number;

    // for breakdowns, we can see the before and after answers
    workDate1: EccoDateTime;
    answerChoiceStr1: string;
    workDate2: EccoDateTime;
    answerChoiceStr2: string;

    // reference to useful stuff
    breakdown: Sequence<QuestionnaireWorkDto>; // we could do some Group here
}

// *********
// CALCULATIONS

/**
 * Takes a series of referrals and works out the social value calculation.
 * NB should include all services, as hact answers can be recorded across referrals
 */
export var hactSocialValueAnalyser: Transformer<ReferralAggregate, HactSocialValue> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<HactSocialValue> {
    return new HactSocialValueAnalysis(ctx, hactSocialValues(groupByClientId(input)));
};

function groupByClientId(input: Sequence<ReferralAggregate>): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.referral.clientId.toString() || "no c-id")
        .pairs()
        .map(extractPair);
}

function hactSocialValues(input: Sequence<Group<ReferralAggregate>>): Sequence<HactSocialValue> {
    return (
        input
            .map(pair => {
                const clientId = parseInt(pair.key);
                const clientDisplayName = pair.elements.first().referral.clientDisplayName;
                const client = pair.elements.first().client;
                const sessionData = pair.elements.first().sessionData!;
                const hactSessionData = pair.elements.first().hactSessionData!;
                const reportCriteria = pair.elements.first().reportCriteria!;

                // NOT IDEAL
                // using a referral-based report (not a client-based one) means the server
                // side ordering is circumvented - it should be workDate desc, created desc
                // see QuestionnaireEvidenceController.findByClientIdAndEvidenceGroupKey
                const clientHactAnswerWork = pair.elements
                    .map(ra => ra.questionnaireWork)
                    .flatten<QuestionnaireWorkDto>()
                    .sort((a, b) => {
                        return sortClientWork(a, b);
                    });
                // NOT IDEAL

                let fromDte = EccoDate.parseIso8601(reportCriteria.from)!;
                let toDte = EccoDate.parseIso8601(reportCriteria.to)!;

                // transforms the client's answers (from the built HactAnswerHistorySummary)
                // into a question based array for the client
                // NB this method has broken the <Sequence>
                let svInt = new SocialValueSummaryDataFull(
                    clientId,
                    clientHactAnswerWork,
                    fromDte,
                    toDte
                );

                // return an intermediate step - useful in debugging the clobbered <Sequence>
                return {
                    summary: svInt,
                    clientDisplayName: clientDisplayName,
                    client: client,
                    sessionData: sessionData,
                    hactSessionData: hactSessionData
                };
            })

            // calculate social value per question
            .map(data => {
                return data.summary.getSocialValuePerQuestion().map(svPerQn => {
                    return createSocialValueForQuestion(
                        svPerQn,
                        data.clientDisplayName,
                        data.client!,
                        data.summary.getClientQuestionnaireWork(),
                        data.sessionData,
                        data.hactSessionData
                    );
                });
            })
            .flatten<HactSocialValue>()
    );

    // don't filter non-valuable, as this helps users see/confirm data consistency of non-valuable changes
    // .filter((data) => {
    //     return data.valuableChange == true;
    // });
}

function sortClientWork(x: BaseWork, y: BaseWork) {
    let xWorkDte = EccoDateTime.parseIso8601(x.workDate);
    let xCreatedDte = EccoDateTime.parseIso8601(x.createdDate);
    let yWorkDte = EccoDateTime.parseIso8601(y.workDate);
    let yCreatedDte = EccoDateTime.parseIso8601(y.createdDate);

    // return 1 to be at the end of the sequence, -1 to be at the start
    // we ignore equals (0)
    // so earlier dates need to be at the end, 1
    if (xWorkDte.equals(yWorkDte)) {
        return xCreatedDte.earlierThan(yCreatedDte) ? 1 : -1;
    }
    if (xWorkDte.earlierThan(yWorkDte)) {
        return 1;
    }
    return -1;
}

// construct the breakdown data which gives the best attempt of the client before/after
function createSocialValueForQuestion(
    input: SocialValueIntermediateData,
    clientDisplayName: string,
    client: Client,
    breakdownData: Sequence<QuestionnaireWorkDto>,
    sessionData: SessionData,
    hactSessionData: HactSessionData
): HactSocialValue | null {
    if (!input) {
        return null;
    }

    // answerValues are string values but are the value of the answer choice (if there is one)
    // (see QuestionnaireHistoryItemControl)
    // for HACT, its always an answer choice value, so we can find the id here
    const answerChoiceDefId1 = sessionData.getAnswerValueId(
        input.questionDefId,
        input.answerValueBefore
    )!;
    const answerChoiceDefId2 = sessionData.getAnswerValueId(
        input.questionDefId,
        input.answerValueAfter
    )!;

    // NB we can't currently know the hact outcome for the question unless its valuable
    // eg - EMP1401, EMP1402. EMP1403 (employment) all refer to the same question
    const valuableHactOutcome = hactSessionData.getValuableChangeOutcome(
        input.questionDefId,
        answerChoiceDefId1,
        answerChoiceDefId2
    )!;

    const clientBirthDate = EccoDate.parseIso8601(client.birthDate!);
    const ageAtValuableAnswer = tableRepresentations.getAgeInYearsBetweenEccoDates(
        clientBirthDate,
        input.answerValueAfterDate.toEccoDate()
    );

    const hactOutcome = hactSessionData.getOutcome(valuableHactOutcome);
    let hactOutcomeValue = 0;
    if (hactOutcome) {
        if (ageAtValuableAnswer == null) {
            hactOutcomeValue = hactOutcome.valueOutsideLondonUnknown;
        } else if (ageAtValuableAnswer < 25) {
            hactOutcomeValue = hactOutcome.valueOutsideLondon24;
        } else if (ageAtValuableAnswer < 50) {
            hactOutcomeValue = hactOutcome.valueOutsideLondon49;
        } else {
            hactOutcomeValue = hactOutcome.valueOutsideLondonHigher;
        }
    }

    // text values useful in the row output
    let answerChoices1 = sessionData.getQuestionById(input.questionDefId).choices.filter(ans => {
        return ans.value == input.answerValueBefore;
    });
    let answerChoices2 = sessionData.getQuestionById(input.questionDefId).choices.filter(ans => {
        return ans.value == input.answerValueAfter;
    });
    let questionName = sessionData.getQuestionById(input.questionDefId).name;

    return {
        clientCode: client.code || client.clientId!.toString(), // was got from referral.clientCode || clientId.toString();
        clientDisplayName: clientDisplayName,
        client: client,
        clientAgeAtValuableAnswer: ageAtValuableAnswer,
        questionDefId: input.questionDefId,
        questionName: questionName,
        answerChoiceDefId1: answerChoiceDefId1,
        answerChoiceDefId2: answerChoiceDefId2,
        valuableChange: valuableHactOutcome != null,
        valuableHactOutcomeDefCode: valuableHactOutcome,
        valuableHactValue: hactOutcomeValue,

        workDate1: input.answerValueBeforeDate,
        answerChoiceStr1: answerChoices1.length > 0 ? answerChoices1[0].displayValue : "",
        workDate2: input.answerValueAfterDate,
        answerChoiceStr2: answerChoices2.length > 0 ? answerChoices2[0].displayValue : "",
        breakdown: breakdownData
    };
}
