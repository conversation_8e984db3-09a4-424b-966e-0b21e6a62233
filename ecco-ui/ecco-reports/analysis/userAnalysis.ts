///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import types = require("./types");
import Analyser = types.Analyser;
import Group = types.Group;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import {User} from "ecco-dto";
import {AclEntryDto} from "ecco-dto";
import {extractPair, GroupFn} from "./types";
import {AnalysisContext} from "../chart-domain";
import {Individual} from "ecco-dto";
import {SessionData} from "ecco-dto";
import {
    booleanColumn,
    columnMap,
    dateTimeColumnFromIsoUtc,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import {individualOnlyColumns} from "../tables/predefined-table-representations";

//*********************************
// Generalised functions

export interface GroupFnProperty<T> {
    (input: Sequence<T>, property: keyof T, ctx?: AnalysisContext): Sequence<Group<T>>;
}
function countsByProperty<T>(
    input: Sequence<T>,
    ctx: AnalysisContext,
    groupFn: GroupFnProperty<T>,
    property: keyof T
): Sequence<Group<T>> {
    return groupFn(input, property, ctx).map(pair => {
        let input: Sequence<T> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}
function groupByProperty<T>(input: Sequence<T>, property: keyof T): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => `${inputElement[property]}`) // not actually compile-time typesafe, but countsBy is
        .pairs()
        .map(extractPair);
}

function countsBy<T>(
    input: Sequence<T>,
    ctx: AnalysisContext,
    groupFn: GroupFn<T>
): Sequence<Group<T>> {
    return groupFn(input, ctx).map(pair => {
        let input: Sequence<T> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}

//*********************************
// Analysis: breakdown of users

const userOnlyColumns = columnMap(
    // timestamp is created time
    numberColumn<User>("u-id", row => row.userId),
    textColumn<User>("username", row => row.username),
    textColumn<User>("first name", row => row.individual.firstName),
    textColumn<User>("last name", row => row.individual.lastName),
    booleanColumn<User>("enabled", row => row.enabled),
    booleanColumn<User>("mfa required", row => row.mfaRequired),
    booleanColumn<User>("mfa validated", row => row.mfaValidated),
    dateTimeColumnFromIsoUtc<User>("registered", row => row.registered),
    dateTimeColumnFromIsoUtc<User>("lastLoggedIn", row => row.lastLoggedIn),
    textColumn<User>("groups", row => row.groups.join())
);

const userToIndividualColumns = joinNestedPathColumnMaps<User, Individual>(
    "i",
    row => row.individual,
    individualOnlyColumns
);
const userWithIndividualColumns = joinColumnMaps(userOnlyColumns, userToIndividualColumns);

export class UserOnlyAnalysis extends SequenceAnalysis<User> {
    constructor(ctx: AnalysisContext, data: Sequence<User>) {
        super(ctx, data, (item: User) => item.username);
        this.recordRepresentation = {
            UserOnly: userWithIndividualColumns // NB we get the individual already from the server with the User
        };
        this.derivativeAnalysers = {
            userCountsByEnabled: userCountsByEnabledAnalyser,
            flattenToAclEntries: flattenToAclEntriesAnalyser
        };
    }
}

//*********************************
// Analysers: User

const userCountsByEnabledAnalyser: Analyser<Sequence<User>, Sequence<Group<User>>> = function (
    ctx: AnalysisContext,
    input: Sequence<User>
): GroupedUserAnalysis {
    return new GroupedUserAnalysis(
        ctx,
        countsByProperty<User>(input, ctx, groupByProperty, "enabled")
    ); // this is type-checked
};

const flattenToAclEntriesAnalyser: Transformer<User, AclEntryDto> = function (
    ctx: AnalysisContext,
    input: Sequence<User>
): SequenceAnalysis<AclEntryDto> {
    return new AclEntryOnlyAnalysis(ctx, flattenToAclEntryWithUser(input));
};
function flattenToAclEntryWithUser(input: Sequence<User>): Sequence<AclEntryDto> {
    const acls: Sequence<AclEntryDto> = input
        .filter(u => u != null && u.acls != null)
        .map(u => {
            const servicesTotal = u.acls!.filter(a => a.clazz.indexOf("ervice") > -1).length;
            return u.acls!.map(a => {
                a.user = u;
                a.servicesTotal = servicesTotal;
                return a;
            });
        })
        .flatten<AclEntryDto>();
    return acls;
}

//*********************************
// Analysis: grouped user functions

class GroupedUserAnalysis extends SequenceAnalysis<Group<User>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<User>>) {
        super(ctx, data, (item: Group<User>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapUserSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", UserOnlyAnalysis);
    }
}

/** This deals with clicking on chart segment */
const WrapUserSequenceAnalyser: Analyser<Group<User>, Sequence<User>> = function (
    ctx: AnalysisContext,
    input: Group<User>
): UserOnlyAnalysis {
    return new UserOnlyAnalysis(ctx, input.elements);
};

//*********************************
// Analysis: breakdown of aclEntries

const aclEntryOnlyColumns = columnMap(
    textColumn<AclEntryDto>("username", row => row.username),
    textColumn<AclEntryDto>("type", row => row.clazz),
    numberColumn<AclEntryDto>("id", row => row.secureObjectId),
    textColumn<AclEntryDto>("name", (row, ctx) => objectName(ctx.getSessionData(), row)),
    numberColumn<AclEntryDto>("permission", row => row.permissionMask),
    numberColumn<AclEntryDto>("services", row => row.servicesTotal)
);
const aclToUserColumns = joinNestedPathColumnMaps<AclEntryDto, User>(
    "u",
    row => row.user,
    userOnlyColumns
);
const aclEntryColumns = joinColumnMaps(aclEntryOnlyColumns, aclToUserColumns);

class AclEntryOnlyAnalysis extends SequenceAnalysis<AclEntryDto> {
    constructor(ctx: AnalysisContext, data: Sequence<AclEntryDto>) {
        super(ctx, data, (item: AclEntryDto) => item.secureObjectId.toString());
        this.recordRepresentation = {
            AclEntryOnly: aclEntryOnlyColumns,
            AclEntry: aclEntryColumns
        };
        this.derivativeAnalysers = {
            aclEntryCountsByClass: aclEntryCountsByClassAnalyser,
            aclEntryCountsById: aclEntryCountsBySecureObjectIdAnalyser
        };
    }
}

//*********************************
// Analysers: AclEntryDto

function objectName(sessionData: SessionData, ace: AclEntryDto): string | null {
    return ace.clazz.indexOf("ervice") > -1
        ? sessionData.getServiceName(ace.secureObjectId)
        : ace.secureObjectId == -1
        ? "can access all projects"
        : sessionData.getProjectName(ace.secureObjectId);
}

const aclEntryCountsByClassAnalyser: Analyser<
    Sequence<AclEntryDto>,
    Sequence<Group<AclEntryDto>>
> = function (ctx: AnalysisContext, input: Sequence<AclEntryDto>): GroupedAclEntryAnalysis {
    return new GroupedAclEntryAnalysis(
        ctx,
        countsByProperty<AclEntryDto>(input, ctx, groupByProperty, "clazz")
    );
};
const aclEntryCountsBySecureObjectIdAnalyser: Analyser<
    Sequence<AclEntryDto>,
    Sequence<Group<AclEntryDto>>
> = function (ctx: AnalysisContext, input: Sequence<AclEntryDto>): GroupedAclEntryAnalysis {
    return new GroupedAclEntryAnalysis(ctx, countsBy<AclEntryDto>(input, ctx, groupByAclEntryId));
};
function groupByAclEntryId(
    input: Sequence<AclEntryDto>,
    ctx?: AnalysisContext
): Sequence<Group<AclEntryDto>> {
    return input
        .groupBy(inputElement => objectName(ctx!.getSessionData(), inputElement) || "")
        .pairs()
        .map(extractPair);
}
//*********************************
// Analysis: grouped acl functions

class GroupedAclEntryAnalysis extends SequenceAnalysis<Group<AclEntryDto>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<AclEntryDto>>) {
        super(ctx, data, (item: Group<AclEntryDto>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapAclEntrySequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", AclEntryOnlyAnalysis);
    }
}

/** This deals with clicking on chart segment */
const WrapAclEntrySequenceAnalyser: Analyser<Group<AclEntryDto>, Sequence<AclEntryDto>> = function (
    ctx: AnalysisContext,
    input: Group<AclEntryDto>
): AclEntryOnlyAnalysis {
    return new AclEntryOnlyAnalysis(ctx, input.elements);
};
