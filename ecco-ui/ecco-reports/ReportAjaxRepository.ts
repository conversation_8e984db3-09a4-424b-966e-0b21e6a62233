import {EccoDate} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {AddressHistoryDto, Individual, RepairDto} from "ecco-dto";
import {ApiClient} from "ecco-dto";
import {Building} from "ecco-dto";
import {ClientAttendanceDto} from "ecco-dto";
import {
    EvidenceGroup,
    BaseServiceRecipientCommandDto,
    BaseActionInstanceSnapshotDto,
    SupportSmartStepsSnapshotDto,
    SupportWork,
    TaskStatus,
    FormEvidence
} from "ecco-dto";
import type { SupportFlags } from "ecco-dto";
import {FinanceReceiptDto} from "ecco-dto";
import {InvoiceLineDto} from "ecco-dto";
import {QuestionnaireAnswersSnapshotDto, QuestionnaireWorkDto} from "ecco-dto";
import {ReferralDto, ServiceRecipientAssociatedContact} from "ecco-dto";
import {RiskGroupEvidenceDto} from "ecco-dto";
import {Client} from "ecco-dto";
import {Agency} from "ecco-dto";
import {RiskFlags, RiskWorkEvidenceDto} from "ecco-dto";
import {SelectionCriteriaDto} from "ecco-dto";
import {ReportCriteriaDto} from "ecco-dto";
import {Review} from "ecco-dto";
import {User} from "ecco-dto";
import {WorkAnalysisGroupSummary} from "./analysis/groupedSummaryCommonAnalysis";
import {CountsByMonthDto, GroupSummary} from "./analysis/types";
import {ReportRepository} from "./ReportRepository";
import dtoReport = require("./dto");

interface WorkAnalysisGroupSummaryDto extends GroupSummary {
    key2: string;
    latestWorkDate: string;
    lastSignedWorkDate: string;
    lastUnSignedWorkDate: string;
    totalTimeSpentMins: number;
    unsignedWorkCount: number;
    totalVisits: number;
    averageVisitLength: number;
    countByCommentType: {[key: string]: number};
}

export class ReportAjaxRepository implements ReportRepository {
    public static instance = new ReportAjaxRepository(apiClient);

    constructor(private apiClient: ApiClient) {}

    public findAllReferrals(reportDto: ReportCriteriaDto, page?: number): Promise<ReferralDto[]> {
        let path = "reports/referrals/";
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return this.apiClient.postWithReAuth<ReferralDto[]>(path, reportDto);
    }

    // for paged reporting
    public findAllReferralSummary(
        reportDto: ReportCriteriaDto | Promise<ReportCriteriaDto>,
        page?: number
    ): Promise<ReferralDto[]> {
        let path = "reports/referrals/summary/";
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return Promise.resolve(reportDto).then(criteria =>
            this.apiClient.postWithReAuth<ReferralDto[]>(path, criteria)
        );
    }

    public static generateLiveReportCriteria(): Promise<ReportCriteriaDto> {
        const liveReferralsDto: SelectionCriteriaDto = {
            referralStatus: "liveAtEnd", // can't use EntityStatus as yet as if referralStatus is blank it gets 'received' in processLegacySelectionCriteria
            selectionRootEntity: "ReferralSummary", // reports do use ReferralSummary anyway - as does the repo call above
            fetchRelatedEntities: [],
            selectorType: null,
            selectionPropertyPath: null,
            serviceRecipientFilter: null,
            userId: null,
            username: null
        };
        return import("./chart-domain").then(({SelectionCriteria}) => {
            const liveReferrals = new SelectionCriteria(liveReferralsDto);
            return liveReferrals.getReportCriteriaDto();
        });
    }

    public findAllSupportWork(reportDto: ReportCriteriaDto, page?: number): Promise<SupportWork[]> {
        // this path mimics findSupportWorkByReferralId
        let path = `reports/evidence/${EvidenceGroup.needs.name}/`;
        if (reportDto.supportEvidenceGroup) {
            path = `${path}${reportDto.supportEvidenceGroup}/`; // NOTE: This seems wrong, but matches ReportController.needsWorkByCriteria
        }
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return this.apiClient.postWithReAuth<SupportWork[]>(path, reportDto);
    }

    public findAllRiskWork(
        reportDto: ReportCriteriaDto,
        page?: number
    ): Promise<RiskWorkEvidenceDto[]> {
        // this path mimics findSupportWorkByReferralId
        let path = `reports/evidence/${EvidenceGroup.threat.name}/`;
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return this.apiClient.postWithReAuth<RiskWorkEvidenceDto[]>(path, reportDto);
    }

    public findAllRiskFlags(reportDto: ReportCriteriaDto, page?: number): Promise<RiskFlags[]> {
        // this path mimics findSupportWorkByReferralId
        return this.apiClient.postWithReAuth<RiskFlags[]>(
            `reports/risk-flags/page/${page}/`,
            reportDto
        );
    }

    public findAllRiskRatings(
        reportDto: ReportCriteriaDto,
        page?: number
    ): Promise<RiskGroupEvidenceDto[]> {
        // this path mimics findAllRiskFlags
        return this.apiClient.postWithReAuth<RiskGroupEvidenceDto[]>(
            `reports/risk-ratings/page/${page}/`,
            reportDto
        );
    }

    public findAllSupportFlags(
        reportDto: ReportCriteriaDto,
        page?: number
    ): Promise<SupportFlags[]> {
        return this.apiClient.postWithReAuth<SupportFlags[]>(
            `reports/support-flags/page/${page}/`,
            reportDto
        );
    }

    public findAllFormEvidenceLatestSnapshot(
        reportDto: ReportCriteriaDto,
        evidenceGroup: EvidenceGroup,
        page?: number
    ): Promise<FormEvidence<any>[]> {
        // this path mimics findSupportWorkByReferralId
        let path = `reports/evidence/form/work/snapshot/latest/`;
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return this.apiClient.postWithReAuth<FormEvidence<any>[]>(path, reportDto);
    }

    public findAllClients(reportDto: ReportCriteriaDto, page: number): Promise<Client[]> {
        const path = `reports/clients/page/${page}/`;
        return this.apiClient.postWithReAuth<Client[]>(path, reportDto);
    }

    public findAllAgencies(reportDto: ReportCriteriaDto, page: number): Promise<Agency[]> {
        return this.apiClient.postWithReAuth<Agency[]>(`reports/agencies/page/${page}/`, reportDto);
    }

    public findAllProfessionals(reportDto: ReportCriteriaDto, page: number): Promise<Individual[]> {
        return this.apiClient.postWithReAuth<Individual[]>(
            `reports/professionals/page/${page}/`,
            reportDto
        );
    }

    public findAllQuestionnaireEarliestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<QuestionnaireAnswersSnapshotDto[]> {
        const path = `reports/evidence/questionnaire/snapshot/earliestInRange/page/${page}/`;
        return this.apiClient.postWithReAuth<QuestionnaireAnswersSnapshotDto[]>(path, reportDto);
    }

    public findAllQuestionnaireLatestSnapshotsBeforeRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<QuestionnaireAnswersSnapshotDto[]> {
        const path = `reports/evidence/questionnaire/snapshot/latestBeforeRange/page/${page}/`;
        return this.apiClient.postWithReAuth<QuestionnaireAnswersSnapshotDto[]>(path, reportDto);
    }

    public findAllQuestionnaireLatestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<QuestionnaireAnswersSnapshotDto[]> {
        const path = `reports/evidence/questionnaire/snapshot/latestInRange/page/${page}/`;
        return this.apiClient.postWithReAuth<QuestionnaireAnswersSnapshotDto[]>(path, reportDto);
    }

    public findAllQuestionnaireWork(
        reportDto: ReportCriteriaDto,
        questionnaireEvidenceGroup: EvidenceGroup,
        page: number | undefined
    ): Promise<QuestionnaireWorkDto[]> {
        const path = questionnaireEvidenceGroup
            ? `reports/evidence/questionnaire/${questionnaireEvidenceGroup.name}/page/${page}/`
            : `reports/evidence/questionnaire/page/${page}/`;
        return this.apiClient.postWithReAuth<QuestionnaireWorkDto[]>(path, reportDto);
    }

    public findAllSmartStepsEarliestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<BaseActionInstanceSnapshotDto[]> {
        const path =
            "threat" == reportDto.supportEvidenceGroup?.toLowerCase()
                ? `reports/evidence/threat/snapshot/earliestInRange/page/${page}/`
                : `reports/evidence/needs/snapshot/earliestInRange/page/${page}/`;
        return this.apiClient.postWithReAuth<SupportSmartStepsSnapshotDto[]>(path, reportDto);
    }

    public findAllSmartStepsLatestSnapshotsBeforeRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<BaseActionInstanceSnapshotDto[]> {
        const path =
            "threat" == reportDto.supportEvidenceGroup?.toLowerCase()
                ? `reports/evidence/threat/snapshot/latestBeforeRange/page/${page}/`
                : `reports/evidence/needs/snapshot/latestBeforeRange/page/${page}/`;
        return this.apiClient.postWithReAuth<SupportSmartStepsSnapshotDto[]>(path, reportDto);
    }

    public findAllSmartStepsLatestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<BaseActionInstanceSnapshotDto[]> {
        const path =
            "threat" == reportDto.supportEvidenceGroup?.toLowerCase()
                ? `reports/evidence/threat/snapshot/latestInRange/page/${page}/`
                : `reports/evidence/needs/snapshot/latestInRange/page/${page}/`;
        return this.apiClient.postWithReAuth<SupportSmartStepsSnapshotDto[]>(path, reportDto);
    }

    public findSupportWorkLatestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<SupportWork[]> {
        const path = `reports/evidence/needs/work/snapshot/latestInRange/page/${page}/`;
        return this.apiClient.postWithReAuth<SupportWork[]>(path, reportDto);
    }

    public findRiskWorkLatestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<RiskWorkEvidenceDto[]> {
        const path = `reports/evidence/threat/work/snapshot/latestInRange/page/${page}/`;
        return this.apiClient.postWithReAuth<RiskWorkEvidenceDto[]>(path, reportDto);
    }

    public findActivityAttendancesByStartDate(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<ClientAttendanceDto[]> {
        const path = `reports/activities/attendance/page/${page}/`;
        return this.apiClient.postWithReAuth<ClientAttendanceDto[]>(path, reportDto);
    }

    // order by created ASC
    // see also ServiceRecipientAjaxRepository#findServiceRecipientCommandsByCreated
    public findServiceRecipientCommandsByCreated(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<BaseServiceRecipientCommandDto[]> {
        const path = `reports/service-recipients/commands/page/${page}/`;
        return this.apiClient.postWithReAuth<BaseServiceRecipientCommandDto[]>(path, reportDto);
    }

    public findServiceRecipientDeletedCommandsByCreated(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<BaseServiceRecipientCommandDto[]> {
        const path = `reports/service-recipients/commands/deleted/page/${page}/`;
        return this.apiClient.postWithReAuth<BaseServiceRecipientCommandDto[]>(path, reportDto);
    }

    public findAllTaskStatuses(reportDto: ReportCriteriaDto, page: number): Promise<TaskStatus[]> {
        return this.apiClient.postWithReAuth<TaskStatus[]>(
            `reports/tasks/page/${page}/`,
            reportDto
        );
    }

    /**
     * This is for the initial 'charts' page
     */
    public findSmartSteps(reportDto: ReportCriteriaDto): Promise<dtoReport.ActionDto[]> {
        return this.apiClient.postWithReAuth<dtoReport.ActionDto[]>(
            "reports/smartsteps/",
            reportDto
        );
    }

    /**
     * This is for the 'dashboards' page we created to hold the latest ideas around dashboards
     */
    public findSupportPlanCommentsUnmanaged(
        fromDte: EccoDate,
        toDte: EccoDate
    ): Promise<dtoReport.EvidenceComment[]> {
        const path = `reports/dashboard/supportCommentsUnmanaged/${fromDte.formatIso8601()}/${toDte.formatIso8601()}/`;
        return this.apiClient.get<dtoReport.EvidenceComment[]>(path);
    }

    public findCountsByQuestion(reportDto: ReportCriteriaDto): Promise<CountsByMonthDto[]> {
        const path = `reports/answersByQuestion/groupBy/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findCountsByService(reportDto: ReportCriteriaDto): Promise<CountsByMonthDto[]> {
        const path = `reports/referralsByService/groupBy/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findCountsBySource(reportDto: ReportCriteriaDto): Promise<CountsByMonthDto[]> {
        const path = `reports/referralsBySource/groupBy/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findCountsByEthnicity(reportDto: ReportCriteriaDto): Promise<CountsByMonthDto[]> {
        const path = `reports/referralsByEthnicity/groupBy/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findCountsBySexualOrientation(
        reportDto: ReportCriteriaDto
    ): Promise<CountsByMonthDto[]> {
        const path = `reports/referralsBySexualOrientation/groupBy/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findCountsByDisability(reportDto: ReportCriteriaDto): Promise<CountsByMonthDto[]> {
        const path = `reports/referralsByDisability/groupBy/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findCountsByMonth(
        reportDto: ReportCriteriaDto,
        dateField: string
    ): Promise<CountsByMonthDto[]> {
        const path = `reports/referralsByMonth/groupBy/${dateField}/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findTasksCountsByMonth(
        reportDto: ReportCriteriaDto,
        groupBy: string
    ): Promise<CountsByMonthDto[]> {
        const path = `reports/tasksByMonth/groupBy/${groupBy}/`;
        return this.apiClient.postWithReAuth<CountsByMonthDto[]>(path, reportDto);
    }

    public findGroupedWorkAnalysis(
        reportDto: ReportCriteriaDto,
        groupBy: string
    ): Promise<WorkAnalysisGroupSummary[]> {
        const path = `reports/groupedWorkAnalysis/${EvidenceGroup.needs.name}/groupBy/${groupBy}/`;

        return this.apiClient
            .postWithReAuth<WorkAnalysisGroupSummaryDto[]>(path, reportDto)
            .then((dtos: WorkAnalysisGroupSummaryDto[]) =>
                dtos.map(dto => ({
                    key: [dto.key, dto.key2].filter(Boolean).join(" "),
                    keyId: dto.keyId,
                    count: dto.count,
                    totalVisits: dto.totalVisits,
                    totalTimeSpentMins: dto.totalTimeSpentMins,
                    averageVisitLength: dto.averageVisitLength,
                    unsignedWorkCount: dto.unsignedWorkCount,
                    latestWorkDate: EccoDate.parseIso8601FromDateTime(dto.latestWorkDate),
                    lastUnSignedWorkDate: EccoDate.parseIso8601FromDateTime(
                        dto.lastUnSignedWorkDate
                    ),
                    lastSignedWorkDate: EccoDate.parseIso8601FromDateTime(dto.lastSignedWorkDate),
                    countByCommentType: dto.countByCommentType
                }))
            );
    }

    public findAllBuildings(reportDto: ReportCriteriaDto, page: number): Promise<Building[]> {
        let path = "reports/buildings/";
        path = `${path}page/${page}/`;
        return this.apiClient.postWithReAuth<Building[]>(path, reportDto);
    }

    public findAllRepairs(reportDto: ReportCriteriaDto, page: number): Promise<RepairDto[]> {
        let path = "reports/repairs/";
        path = `${path}page/${page}/`;
        return this.apiClient.postWithReAuth<RepairDto[]>(path, reportDto);
    }

    public findAllFinanceCharges(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<InvoiceLineDto[]> {
        let path = "reports/financeCharges/";
        path = `${path}page/${page}/`;
        return this.apiClient.postWithReAuth<InvoiceLineDto[]>(path, reportDto);
    }

    public findAllFinanceReceipts(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<FinanceReceiptDto[]> {
        let path = "reports/financeReceipts/";
        path = `${path}page/${page}/`;
        return this.apiClient.postWithReAuth<FinanceReceiptDto[]>(path, reportDto);
    }

    public findAllUsers(reportDto: ReportCriteriaDto, page?: number): Promise<User[]> {
        // this path mimics findSupportWorkByReferralId
        let path = "reports/users/";
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return this.apiClient.postWithReAuth<User[]>(path, reportDto);
    }

    public findAllReviews(reportDto: ReportCriteriaDto, page?: number): Promise<Review[]> {
        let path = "reports/reviews/";
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return this.apiClient.postWithReAuth<Review[]>(path, reportDto);
    }

    public findAllAddressHistory(
        reportDto: ReportCriteriaDto,
        page?: number
    ): Promise<AddressHistoryDto[]> {
        let path = "reports/addresshistory/";
        if (page != null) {
            path = `${path}page/${page}/`;
        }
        return this.apiClient.postWithReAuth<AddressHistoryDto[]>(path, reportDto);
    }

    public findAllAssociatedContacts(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<ServiceRecipientAssociatedContact[]> {
        const path = `reports/contacts/page/${page}/`;
        return this.apiClient.postWithReAuth<ServiceRecipientAssociatedContact[]>(path, reportDto);
    }
}
