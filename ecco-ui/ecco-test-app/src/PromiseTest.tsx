import * as React from "react";
import {FC, useState} from "react";
import {usePromise} from "ecco-components/data/entityLoadHooks";
import {useServicesContext} from "ecco-components/ServicesContext";
import {stringifyPossibleError} from "ecco-offline-data";

const delay = (ms: number | undefined) => new Promise(_ => setTimeout(_, ms));

// MIMIC entityLoadHooks useUser, but with an added delay
// alternatively we could replace ServiceContext with a different clientAPI
export function useUser(username: string) {
    const {userRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => userRepository.findOneUser(username)
            .then(user =>
                delay(1000).then(() => user)),
        [username]);
    return {user: resolved, error, loading};
}

// component to do the output of the promise
export const UserView: FC<{ username: string }> = props => {
    const {user, error, loading} = useUser(props.username);

    if (!user && !error && !loading) {
        console.error("BROKEN");
    }
    return (
        <>
            <div>user: {user?.username || "undefined"}</div>
            <div>loading: {loading ? "true" : "false"}</div>
            <div>error: {stringifyPossibleError(error)}</div>
            <br />
        </>
    );
}

const PromiseTest = () => {

    const [username, setUsername] = useState<string>("sysadmin");

    setTimeout(() => {  setUsername("no-user")}, 3000);

    return <>
        <div>watch the results of calling a promise:</div>
        <br/>
        <UserView username={username}/>
        <UserView username={username}/>
        <UserView username={username}/>
        <UserView username={username}/>
        <UserView username={username}/>
        </>;
}

export default PromiseTest;
