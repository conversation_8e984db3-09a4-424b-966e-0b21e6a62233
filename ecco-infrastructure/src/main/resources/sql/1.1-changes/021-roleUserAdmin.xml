<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- introduce ROLE_LOGINADMIN (allows ldap) to useradmin (which can already 'impersonate user') - typically sysadmin -->
    <changeSet author="adamjhamer" id="ECCO-270-5">
        <preConditions onFail="MARK_RAN">
            <!-- check we are only dealing with one user, which we can assume is sysadmin -->
            <!-- use count logic since liquibase has to have a result -->
            <sqlCheck expectedResult="1">select count(1) from group_members where group_id=7</sqlCheck>
            <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        </preConditions>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="7"/>
            <column name="authority" value="ROLE_LOGINADMIN"/>
        </insert>
    </changeSet>

</databaseChangeLog>
