<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-1">
        <createTable tableName="accommodationCategories">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-2">
        <createTable tableName="accommodations">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="projectId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-3">
        <createTable tableName="actionfakes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(500)"/>
            <column name="riskName" type="VARCHAR(255)"/>
            <column name="realactionid" type="BIGINT"/>
            <column name="riskId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-4">
        <createTable tableName="actionlinks">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="action1_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="action2_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-5">
        <createTable tableName="actions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(500)"/>
            <column name="riskName" type="VARCHAR(255)"/>
            <column name="riskId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-6">
        <createTable tableName="activities">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="capacity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="fromdate" type="DATETIME"/>
            <column name="hours" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="todate" type="DATETIME"/>
            <column name="value" type="DECIMAL(19,2)"/>
            <column name="activitytypeid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="venueid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-7">
        <createTable tableName="activities_clients">
            <column name="attended" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="attending" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="collected" type="DECIMAL(19,2)"/>
            <column name="cost" type="DECIMAL(19,2)"/>
            <column name="version" type="INT"/>
            <column name="clientid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="activityid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-8">
        <createTable tableName="activities_workers">
            <column name="attended" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="attending" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="buddy" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="leader" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INT"/>
            <column name="workerid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="activityid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-9">
        <createTable tableName="activitytypes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="serviceId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-10">
        <createTable tableName="agencycategories">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-11">
        <createTable tableName="audits">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="action" type="VARCHAR(255)"/>
            <column name="class" type="VARCHAR(255)"/>
            <column name="created" type="DATETIME"/>
            <column name="entityid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="usersid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-14">
        <createTable tableName="clientdetails">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="birthday" type="SMALLINT"/>
            <column name="birthmonth" type="SMALLINT"/>
            <column name="hour" type="SMALLINT"/>
            <column name="minute" type="SMALLINT"/>
            <column name="birthyear" type="INT"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="created" type="DATETIME"/>
            <column name="dentistDetails" type="CLOB"/>
            <column name="disability" type="INT"/>
            <column name="doctorDetails" type="CLOB"/>
            <column name="emergencyDetails" type="CLOB"/>
            <column name="gender" type="VARCHAR(255)"/>
            <column name="keycode" type="VARCHAR(255)"/>
            <column name="medicationDetails" type="CLOB"/>
            <column name="militarynumber" type="VARCHAR(255)"/>
            <column name="mothersfirstname" type="VARCHAR(255)"/>
            <column name="nhs" type="VARCHAR(10)"/>
            <column name="ni" type="VARCHAR(255)"/>
            <column name="paris" type="VARCHAR(255)"/>
            <column name="sexuality" type="VARCHAR(255)"/>
            <column name="avatarid" type="BIGINT"/>
            <column name="commenttarget_id" type="BIGINT"/>
            <column name="contactsid" type="BIGINT"/>
            <column name="ethnicoriginsid" type="BIGINT"/>
            <column name="languagesid" type="BIGINT"/>
            <column name="religionsid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-17">
        <createTable tableName="commenttypes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-18">
        <createTable tableName="contacts">
            <column name="discriminator_orm" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="addresscountry" type="VARCHAR(255)"/>
            <column name="addresscounty" type="VARCHAR(255)"/>
            <column name="addressline1" type="VARCHAR(255)"/>
            <column name="addressline2" type="VARCHAR(255)"/>
            <column name="addressline3" type="VARCHAR(255)"/>
            <column name="addresspostcode" type="VARCHAR(255)"/>
            <column name="addresstown" type="VARCHAR(255)"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="mobilenumber" type="VARCHAR(255)"/>
            <column name="phonenumber" type="VARCHAR(255)"/>
            <column name="companyname" type="VARCHAR(50)"/>
            <column name="deliverypartner" type="BOOLEAN"/>
            <column name="internal" type="BOOLEAN"/>
            <column name="outofarea" type="BOOLEAN"/>
            <column name="birthday" type="SMALLINT"/>
            <column name="birthmonth" type="SMALLINT"/>
            <column name="hour" type="SMALLINT"/>
            <column name="minute" type="SMALLINT"/>
            <column name="birthyear" type="INT"/>
            <column name="firstname" type="VARCHAR(255)"/>
            <column name="keyholder" type="BOOLEAN"/>
            <column name="lastname" type="VARCHAR(255)"/>
            <column name="livingwithclient" type="BOOLEAN"/>
            <column name="preferredcontactmethod" type="VARCHAR(255)"/>
            <column name="agencycategoryid" type="BIGINT"/>
            <column name="companyid" type="BIGINT"/>
            <column name="economicstatusid" type="BIGINT"/>
            <column name="usersid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-19">
        <createTable tableName="contacts_events">
            <column name="eventstatus" type="INT"/>
            <column name="version" type="INT"/>
            <column name="contactid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="eventid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-20">
        <createTable tableName="cosmo_attribute">
            <column name="attributetype" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="createdate" type="BIGINT"/>
            <column name="etag" type="VARCHAR(255)"/>
            <column name="modifydate" type="BIGINT"/>
            <column name="localname" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="namespace" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="binvalue" type="LONGBLOB"/>
            <column name="booleanvalue" type="BOOLEAN"/>
            <column name="datevalue" type="DATETIME"/>
            <column name="tzvalue" type="VARCHAR(32)"/>
            <column name="decvalue" type="DECIMAL(19,6)"/>
            <column name="textvalue" type="CLOB"/>
            <column name="intvalue" type="BIGINT"/>
            <column name="stringvalue" type="VARCHAR(2048)"/>
            <column name="itemid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-21">
        <createTable tableName="cosmo_collection_item">
            <column name="createdate" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="itemid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="collectionid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-22">
        <createTable tableName="cosmo_content_data">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="content" type="LONGBLOB"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-23">
        <createTable tableName="cosmo_dictionary_values">
            <column name="attributeid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="stringvalue" type="VARCHAR(2048)"/>
            <column name="keyname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-24">
        <createTable tableName="cosmo_event_log">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="authid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="authtype" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="entrydate" type="BIGINT"/>
            <column name="id1" type="BIGINT"/>
            <column name="id2" type="BIGINT"/>
            <column name="id3" type="BIGINT"/>
            <column name="id4" type="BIGINT"/>
            <column name="strval1" type="VARCHAR(255)"/>
            <column name="strval2" type="VARCHAR(255)"/>
            <column name="strval3" type="VARCHAR(255)"/>
            <column name="strval4" type="VARCHAR(255)"/>
            <column name="eventtype" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="uid1" type="VARCHAR(255)"/>
            <column name="uid2" type="VARCHAR(255)"/>
            <column name="uid3" type="VARCHAR(255)"/>
            <column name="uid4" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-25">
        <createTable tableName="cosmo_event_stamp">
            <column name="icaldata" type="CLOB">
                <constraints nullable="false"/>
            </column>
            <column name="enddate" type="VARCHAR(16)"/>
            <column name="isfloating" type="BOOLEAN"/>
            <column name="isrecurring" type="BOOLEAN"/>
            <column name="startdate" type="VARCHAR(16)"/>
            <column name="stampid" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-26">
        <validCheckSum>7:0dea2a027d48311c860894640c964bde</validCheckSum>
        <createTable tableName="cosmo_item">
            <column name="itemtype" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="createdate" type="BIGINT"/>
            <column name="etag" type="VARCHAR(255)"/>
            <column name="modifydate" type="BIGINT"/>
            <column name="clientcreatedate" type="BIGINT"/>
            <column name="clientmodifieddate" type="BIGINT"/>
            <column name="displayname" type="VARCHAR(1024)"/>
            <column name="itemname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="item_uid" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="lastmodification" type="INT"/>
            <column name="lastmodifiedby" type="VARCHAR(255)"/>
            <column name="needsreply" type="BOOLEAN"/>
            <column name="sent" type="BOOLEAN"/>
            <column name="isautotriage" type="BOOLEAN"/>
            <column name="triagestatuscode" type="INT"/>
            <column name="triagestatusrank" type="DECIMAL(12,2)"/>
            <column name="icaluid" type="VARCHAR(255)"/>
            <column name="contentencoding" type="VARCHAR(32)"/>
            <column name="contentlanguage" type="VARCHAR(32)"/>
            <column name="contentlength" type="BIGINT"/>
            <column name="contenttype" type="VARCHAR(64)"/>
            <column name="hasmodifications" type="BOOLEAN"/>
            <column name="ownerid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="contentdataid" type="BIGINT"/>
            <column name="modifiesitemid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-27">
        <createTable tableName="cosmo_multistring_values">
            <column name="attributeid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="stringvalue" type="VARCHAR(2048)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-28">
        <createTable tableName="cosmo_pwrecovery">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="creationdate" type="DATETIME"/>
            <column name="pwrecoverykey" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="timeout" type="BIGINT"/>
            <column name="userid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-29">
        <createTable tableName="cosmo_server_properties">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="propertyname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="propertyvalue" type="VARCHAR(2048)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-30">
        <createTable tableName="cosmo_stamp">
            <column name="stamptype" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="createdate" type="BIGINT"/>
            <column name="etag" type="VARCHAR(255)"/>
            <column name="modifydate" type="BIGINT"/>
            <column name="itemid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-31">
        <createTable tableName="cosmo_subscription">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="createdate" type="BIGINT"/>
            <column name="etag" type="VARCHAR(255)"/>
            <column name="modifydate" type="BIGINT"/>
            <column name="collectionuid" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="displayname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="ticketkey" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="ownerid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-32">
        <createTable tableName="cosmo_ticket_privilege">
            <column name="ticketid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="privilege" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-33">
        <createTable tableName="cosmo_tickets">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="createdate" type="BIGINT"/>
            <column name="etag" type="VARCHAR(255)"/>
            <column name="modifydate" type="BIGINT"/>
            <column name="creationdate" type="DATETIME"/>
            <column name="ticketkey" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="tickettimeout" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="itemid" type="BIGINT"/>
            <column name="ownerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-34">
        <createTable tableName="cosmo_tombstones">
            <column name="tombstonetype" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="removedate" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="localname" type="VARCHAR(255)"/>
            <column name="namespace" type="VARCHAR(255)"/>
            <column name="itemuid" type="VARCHAR(255)"/>
            <column name="stamptype" type="VARCHAR(255)"/>
            <column name="itemid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-35">
        <createTable tableName="cosmo_user_preferences">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="createdate" type="BIGINT"/>
            <column name="etag" type="VARCHAR(255)"/>
            <column name="modifydate" type="BIGINT"/>
            <column name="preferencename" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="preferencevalue" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="userid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-36">
        <createTable tableName="cosmo_users">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="createdate" type="BIGINT"/>
            <column name="etag" type="VARCHAR(255)"/>
            <column name="modifydate" type="BIGINT"/>
            <column name="activationid" type="VARCHAR(255)"/>
            <column name="admin" type="BOOLEAN"/>
            <column name="email" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="firstname" type="VARCHAR(128)"/>
            <column name="lastname" type="VARCHAR(128)"/>
            <column name="locked" type="BOOLEAN"/>
            <column name="password" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_uid" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="username" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-37">
        <createTable tableName="countries">
            <column name="name" type="VARCHAR(100)"/>
            <column name="friendlyName" type="VARCHAR(100)"/>
            <column defaultValueNumeric="0" name="eu" type="DECIMAL(1,0)">
                <constraints nullable="false"/> <!-- gloss over the fact that this wasn't originally here -->
            </column>
            <column name="iso3166_2alpha" type="VARCHAR(10)"/>
            <column name="iso3166_3alpha" type="VARCHAR(10)"/>
            <column name="dialcode" type="VARCHAR(10)"/>
            <column name="currency" type="VARCHAR(10)"/>
            <column name="med_crd1charge" type="DECIMAL(18,9)"/>
            <column name="med_crd2charge" type="DECIMAL(18,9)"/>
            <column name="med_subscharge" type="DECIMAL(18,9)"/>
            <column name="med_subschargeKS" type="DECIMAL(18,9)"/>
            <column name="med_transfeeKS" type="DECIMAL(18,9)"/>
            <column name="med_unitcharge" type="DECIMAL(18,9)"/>
            <column name="clickatell_units" type="DECIMAL(9,2)"/>
            <column name="med_smscharge1" type="DECIMAL(18,9)"/>
            <column name="med_smscharge2" type="DECIMAL(18,9)"/>
            <column name="med_pccharge1" type="DECIMAL(18,9)"/>
            <column name="med_pccharge2" type="DECIMAL(18,9)"/>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-38">
        <createTable tableName="departments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-39">
        <createTable tableName="economicstatuses">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-40">
        <createTable tableName="ethnicorigins">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-41">
        <createTable tableName="events">
            <column name="type" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="eventday" type="SMALLINT"/>
            <column name="eventmonth" type="SMALLINT"/>
            <column name="eventhour" type="SMALLINT"/>
            <column name="eventminute" type="SMALLINT"/>
            <column name="eventyear" type="INT"/>
            <column name="eventendday" type="SMALLINT"/>
            <column name="eventendmonth" type="SMALLINT"/>
            <column name="eventendhour" type="SMALLINT"/>
            <column name="eventendminute" type="SMALLINT"/>
            <column name="eventendyear" type="INT"/>
            <column name="eventtype" type="INT"/>
            <column name="generated" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="repeatyears" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="cal_uid" type="VARCHAR(255)"/>
            <column name="actionid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="serviceId" type="BIGINT"/>
            <column name="workerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-42">
        <createTable tableName="exitreasons">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-44">
        <createTable tableName="flags">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-49">
        <preConditions onFail="CONTINUE">
            <not>
                <tableExists tableName="hibernate_sequences"/>
            </not>
        </preConditions>
        <createTable tableName="hibernate_sequences">
            <column name="sequence_name" type="VARCHAR(40)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="next_val" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-50">
        <createTable tableName="hr">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-51">
        <createTable tableName="hr_outcomes">
            <column name="hrid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="outcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-52">
        <createTable tableName="hrfromtos">
            <column name="discriminator_orm" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="fromdate" type="DATETIME"/>
            <column name="todate" type="DATETIME"/>
            <column name="value" type="DECIMAL(19,2)"/>
            <column name="entitlementchange" type="DECIMAL(19,2)"/>
            <column name="entitlementchangesessional" type="DECIMAL(19,2)"/>
            <column name="comments" type="CLOB"/>
            <column name="leavereasonid" type="BIGINT"/>
            <column name="workerid" type="BIGINT"/>
            <column name="jobid" type="BIGINT"/>
            <column name="workerjob_jobid" type="BIGINT"/>
            <column name="workerjob_workerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-53">
        <createTable tableName="hrsettings">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="value" type="VARCHAR(255)"/>
            <column name="hrid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-54">
        <createTable tableName="individual_individualTypes">
            <column name="individualId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="individualType" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-55">
        <createTable tableName="jobs">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="sessional" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="departmentid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-56">
        <createTable tableName="languages">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="iso6391" type="VARCHAR(255)"/>
            <column name="iso6392" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-58">
        <createTable tableName="leavereasons">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-59">
        <createTable tableName="localauthorities">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-60">
        <createTable tableName="locales">
            <column name="iso6391" type="VARCHAR(5)"/>
            <column name="iso3166_2alpha" type="VARCHAR(5)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-62">
        <createTable tableName="offences">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-63">
        <createTable tableName="outcomes">
            <column name="discriminator_orm" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="weighting" type="INT"/>
            <column name="questiongroupId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-64">
        <createTable tableName="outcomes_st_referralaspects">
            <column name="outcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="referralaspectId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-65">
        <createTable tableName="outcomethreats_actionsupports">
            <column name="threatoutcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="supportplanactionid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-66">
        <createTable tableName="outcomethreats_questions">
            <column name="threatoutcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="questionId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-67">
        <createTable tableName="pendingstatuses">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-69">
        <createTable tableName="projectcomments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="bc_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="minutesspent" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="logdate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="typeid" type="BIGINT"/>
            <column name="projectId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-70">
        <createTable tableName="projects">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="addresscountry" type="VARCHAR(255)"/>
            <column name="addresscounty" type="VARCHAR(255)"/>
            <column name="addressline1" type="VARCHAR(255)"/>
            <column name="addressline2" type="VARCHAR(255)"/>
            <column name="addressline3" type="VARCHAR(255)"/>
            <column name="addresspostcode" type="VARCHAR(255)"/>
            <column name="addresstown" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="regionId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-71">
        <createTable tableName="qg_st_referralaspects">
            <column name="questiongroupId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="referralaspectId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-72">
        <createTable tableName="questionanswerchoices">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="displayImage" type="VARCHAR(255)"/>
            <column name="displayValue" type="VARCHAR(255)"/>
            <column name="hiddenDefault" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-73">
        <createTable tableName="questionanswerfrees">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="maximum" type="INT"/>
            <column name="minimum" type="INT"/>
            <column name="valueType" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-74">
        <createTable tableName="questiongroups">
            <column name="discriminator_orm" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="headerText" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-75">
        <createTable tableName="questiongroups_questions">
            <column name="questiongroupId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="questionId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-76">
        <createTable tableName="questions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="answerType" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-77">
        <createTable tableName="questions_questionanswerfrees">
            <column name="questionId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="questionanswerfreeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-78">
        <createTable tableName="questions_questionanswrchoices">
            <column name="questionId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="questionanswerchoiceId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-79">
        <createTable tableName="referralactivities">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="ponumber" type="VARCHAR(255)"/>
            <column name="agreementinplace" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="complete" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="fromdate" type="DATETIME"/>
            <column name="fundingband" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="plannedend" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="todate" type="DATETIME"/>
            <column name="value" type="DECIMAL(19,2)"/>
            <column name="clientcalendarentryid" type="VARCHAR(255)"/>
            <column name="actionid" type="BIGINT"/>
            <column name="agencyid" type="BIGINT"/>
            <column name="activitytypeid" type="BIGINT"/>
            <column name="referralid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="supportplanworkid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-80">
        <createTable tableName="referralactivitytypes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="schedulable" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-81">
        <createTable tableName="referralactivityworker">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="calendarentryid" type="VARCHAR(255)"/>
            <column name="supportworkerid" type="BIGINT"/>
            <column name="referralactivityid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-82">
        <createTable tableName="referralaspects">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="display" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="displayOverview" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="external" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="friendlyName" type="VARCHAR(255)"/>
            <column name="internal" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-83">
        <createTable tableName="referralattachments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="filename" type="VARCHAR(255)"/>
            <column name="upload_size" type="BIGINT"/>
            <column name="showonreferral" type="BOOLEAN"/>
            <column name="showonsupportplan" type="BOOLEAN"/>
            <column name="bytesid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="referralid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-84">
        <createTable tableName="referralcomments">
            <column name="discriminator_orm" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="bc_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="minutesspent" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="contactid" type="BIGINT"/>
            <column name="typeid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-85">
        <createTable tableName="referralprojects">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="exited" type="DATETIME"/>
            <column name="receivingproject" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="receivingprojectdate" type="DATETIME"/>
            <column name="accommodationid" type="BIGINT"/>
            <column name="projectId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="referralid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-86">
        <createTable tableName="referrals">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="acceptedexternal" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="acceptedfunding" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="acceptedonservice" type="BOOLEAN"/>
            <column name="acceptedreferral" type="BOOLEAN"/>
            <column name="alcohol" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="anger" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="arson" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="asbo" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="autistic" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="catdisabledfamilies" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="catfather" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="catloneparent" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="catmigrant" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="catminority" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="catteenageparent" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="cattemporaryaccommodation" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="cattraveller" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="catworkless" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="children" type="BOOLEAN"/>
            <column name="chronic" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="comments" type="CLOB"/>
            <column name="contractstartdate" type="DATETIME"/>
            <column name="created" type="DATETIME"/>
            <column name="currentreferralaspect" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="customobjectdata" type="LONGBLOB"/>
            <column name="customstringdata" type="LONGBLOB"/>
            <column name="debt" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="decision" type="BOOLEAN"/>
            <column name="decisiondate" type="DATETIME"/>
            <column name="decisionexternalmadeon" type="DATETIME"/>
            <column name="decisionfundingdate" type="DATETIME"/>
            <column name="decisionmadeon" type="DATETIME"/>
            <column name="decisionreferralmadeon" type="DATETIME"/>
            <column name="deliveredbystartdate" type="DATETIME"/>
            <column name="disabilitynotdisclosed" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="disabledother" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="domesticviolence" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="drugs" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="eligible" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="emotionalhealth" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="exited" type="DATETIME"/>
            <column name="external" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="firstofferedinterviewdate" type="DATETIME"/>
            <column name="firstresponsemadeon" type="DATETIME"/>
            <column name="fundingband" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="fundinghoursofsupport" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="gambling" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="hearing" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="interviewdna" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="interviewDnaComments" type="CLOB"/>
            <column name="interviewSetupComments" type="CLOB"/>
            <column name="interviewersother" type="VARCHAR(255)"/>
            <column name="learningdifficulty" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="life" type="BOOLEAN"/>
            <column name="location" type="VARCHAR(255)"/>
            <column name="mappa" type="INT"/>
            <column name="marac" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="maritalstatus" type="VARCHAR(255)"/>
            <column name="mentalhealth" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="mhastatus" type="VARCHAR(255)"/>
            <column name="mobility" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="offender" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="physicalhealth" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="receiveddate" type="DATETIME"/>
            <column name="receivingservice" type="BOOLEAN"/>
            <column name="receivingservicedate" type="DATETIME"/>
            <column name="referralReason" type="CLOB"/>
            <column name="requiredacceptanceasap" type="BOOLEAN"/>
            <column name="requiredacceptancedate" type="DATETIME"/>
            <column name="selfreferral" type="BOOLEAN"/>
            <column name="sexoffender" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="signpostedback" type="BOOLEAN"/>
            <column name="substance" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="violence" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="violencefromoutside" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="visual" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="waitinglistscore" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="agencyid" type="BIGINT"/>
            <column name="clientid" type="BIGINT"/>
            <column name="accommodationcategoryid" type="BIGINT"/>
            <column name="deliveredbyid" type="BIGINT"/>
            <column name="exitcommentid" type="BIGINT"/>
            <column name="exitreasonid" type="BIGINT"/>
            <column name="fundingsourceid" type="BIGINT"/>
            <column name="interviewer1id" type="BIGINT"/>
            <column name="interviewer2id" type="BIGINT"/>
            <column name="offenceid" type="BIGINT"/>
            <column name="pendingstatusid" type="BIGINT"/>
            <column name="primaryreferralid" type="BIGINT"/>
            <column name="projectpreferredid" type="BIGINT"/>
            <column name="referredserviceid" type="BIGINT"/>
            <column name="referrerid" type="BIGINT"/>
            <column name="sourcetypeid" type="BIGINT"/>
            <column name="signpostedagencyid" type="BIGINT"/>
            <column name="signpostedcommentid" type="BIGINT"/>
            <column name="signpostedparentreferralid" type="BIGINT"/>
            <column name="signpostedreasonid" type="BIGINT"/>
            <column name="signpostedserviceid" type="BIGINT"/>
            <column name="sourceregionid" type="BIGINT"/>
            <column name="supportworkerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-87">
        <createTable tableName="referrals_contacts">
            <column name="referralid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="contactid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-88">
        <createTable tableName="referrersources">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-89">
        <createTable tableName="regions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-90">
        <createTable tableName="religions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-91">
        <createTable tableName="reportattachments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="filename" type="VARCHAR(255)"/>
            <column name="upload_size" type="BIGINT"/>
            <column name="bytesid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="reportid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-92">
        <createTable tableName="reports">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-93">
        <createTable tableName="reviews">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="complete" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="reviewpage" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="startdate" type="DATETIME"/>
            <column name="referralid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-96">
        <createTable tableName="risks">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="outcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-97">
        <createTable tableName="services">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="servicetypeId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-98">
        <createTable tableName="services_projects">
            <column name="serviceId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="projectId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-99">
        <createTable tableName="servicetypes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="accommodation" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="anonymousSupport" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="contractRequired" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="logtime" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="multipleReferrals" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-100">
        <createTable tableName="servicetypes_flagthreats">
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="flagId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-101">
        <createTable tableName="servicetypes_outcomeservices">
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="outcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-102">
        <createTable tableName="servicetypes_outcomesupports">
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="outcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-103">
        <createTable tableName="servicetypes_outcomethreats">
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="threatoutcomeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-104">
        <createTable tableName="servicetypes_questiongroups">
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="questiongroupId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-105">
        <createTable tableName="servicetypes_referralaspects">
            <column name="allowNext" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="orderby" type="INT"/>
            <column name="version" type="INT"/>
            <column name="referralaspectId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-107">
        <createTable tableName="signature">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="signeddate" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="svgxml" type="CLOB">
                <constraints nullable="false"/>
            </column>
            <column name="individualId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-108">
        <createTable tableName="signpostreasons">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="clientDecision" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="organisationDecision" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-109">
        <createTable tableName="st_referralaspectsettings">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="value" type="VARCHAR(255)"/>
            <column name="referralaspectId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="servicetypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-110">
        <createTable tableName="supporthractions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="activity" type="INT"/>
            <column name="ga_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="expirydate" type="DATETIME"/>
            <column name="quantity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschange" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="target" type="DATETIME"/>
            <column name="workdate" type="DATETIME"/>
            <column name="actionid" type="BIGINT"/>
            <column name="contactid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
            <column name="workerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-111">
        <createTable tableName="supporthrcomments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="bc_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="minutesspent" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="contactid" type="BIGINT"/>
            <column name="typeid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
            <column name="workerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-112">
        <createTable tableName="supporthroutcomes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschangedate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="outcomeId" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
            <column name="workerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-113">
        <createTable tableName="supporthrwork">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="workdate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="workerid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-114">
        <createTable tableName="supporthrwork_actions">
            <column name="supporthrworkid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="actionid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-115">
        <createTable tableName="supportplanactions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="activity" type="INT"/>
            <column name="ga_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="expirydate" type="DATETIME"/>
            <column name="quantity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschange" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="target" type="DATETIME"/>
            <column name="workdate" type="DATETIME"/>
            <column name="actionid" type="BIGINT"/>
            <column name="contactid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-116">
        <createTable tableName="supportplananswers">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="answer" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="workdate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="questionId" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-117">
        <createTable tableName="supportplancomments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="bc_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="minutesspent" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="requiresthreatmanagement" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="contactid" type="BIGINT"/>
            <column name="typeid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="threatworkid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-118">
        <createTable tableName="supportplanoutcomes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschangedate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="outcomeId" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-119">
        <createTable tableName="supportplanrisks">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="relevant" type="INT"/>
            <column name="contactid" type="BIGINT"/>
            <column name="riskId" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-120">
        <createTable tableName="supportplanserviceactions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="activity" type="INT"/>
            <column name="ga_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="expirydate" type="DATETIME"/>
            <column name="quantity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschange" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="target" type="DATETIME"/>
            <column name="workdate" type="DATETIME"/>
            <column name="actionid" type="BIGINT"/>
            <column name="contactid" type="BIGINT"/>
            <column name="serviceId" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-121">
        <createTable tableName="supportplanservicecomments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="bc_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="minutesspent" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="contactid" type="BIGINT"/>
            <column name="typeid" type="BIGINT"/>
            <column name="serviceId" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-122">
        <createTable tableName="supportplanserviceoutcomes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschangedate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="outcomeId" type="BIGINT"/>
            <column name="serviceId" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-123">
        <createTable tableName="supportplanservicework">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="workdate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="serviceId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-124">
        <createTable tableName="supportplanservicework_actions">
            <column name="supportplanserviceworkid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="actionid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-125">
        <createTable tableName="supportplanwork">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="asrisk" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME"/>
            <column name="outcomepage" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="sourcepage" type="BIGINT"/>
            <column name="sourcepagegroup" type="BIGINT"/>
            <column name="workdate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="reviewid" type="BIGINT"/>
            <column name="signatureid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-126">
        <createTable tableName="supportplanwork_actions">
            <column name="supportplanworkid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="actionid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-127">
        <createTable tableName="supportthreatactions">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="activity" type="INT"/>
            <column name="ga_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="expirydate" type="DATETIME"/>
            <column name="quantity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschange" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="target" type="DATETIME"/>
            <column name="workdate" type="DATETIME"/>
            <column name="actionid" type="BIGINT"/>
            <column name="contactid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-128">
        <createTable tableName="supportthreatcomments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="bc_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="minutesspent" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="leveloverall" type="INT"/>
            <column name="contactid" type="BIGINT"/>
            <column name="typeid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-129">
        <createTable tableName="supportthreatflags">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="value" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="flagId" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-130">
        <createTable tableName="supportthreatoutcomes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="statuschangedate" type="DATETIME"/>
            <column name="customobjectdata" type="LONGBLOB"/>
            <column name="customstringdata" type="LONGBLOB"/>
            <column name="sto_level" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="levelmeasure" type="VARCHAR(255)"/>
            <column name="contactid" type="BIGINT"/>
            <column name="outcomeId" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
            <column name="workid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-131">
        <createTable tableName="supportthreatwork">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="restriction" type="VARCHAR(255)"/>
            <column name="sourcepage" type="BIGINT"/>
            <column name="sourcepagegroup" type="BIGINT"/>
            <column name="workdate" type="DATETIME"/>
            <column name="contactid" type="BIGINT"/>
            <column name="referralid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-132">
        <createTable tableName="supportthreatwork_actions">
            <column name="supportthreatworkid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="actionid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-137">
        <createTable tableName="venues">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="addresscountry" type="VARCHAR(255)"/>
            <column name="addresscounty" type="VARCHAR(255)"/>
            <column name="addressline1" type="VARCHAR(255)"/>
            <column name="addressline2" type="VARCHAR(255)"/>
            <column name="addressline3" type="VARCHAR(255)"/>
            <column name="addresspostcode" type="VARCHAR(255)"/>
            <column name="addresstown" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-138">
        <createTable tableName="work">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="work_date" type="DATETIME"/>
            <column name="hours" type="DECIMAL(19,2)"/>
            <column name="jobid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="workerid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-139">
        <createTable tableName="workerattachments">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="filename" type="VARCHAR(255)"/>
            <column name="upload_size" type="BIGINT"/>
            <column name="bytesid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="workerid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-140">
        <createTable tableName="workers">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="birthday" type="SMALLINT"/>
            <column name="birthmonth" type="SMALLINT"/>
            <column name="hour" type="SMALLINT"/>
            <column name="minute" type="SMALLINT"/>
            <column name="birthyear" type="INT"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="created" type="DATETIME"/>
            <column name="dentistDetails" type="CLOB"/>
            <column name="disability" type="INT"/>
            <column name="doctorDetails" type="CLOB"/>
            <column name="emergencyDetails" type="CLOB"/>
            <column name="gender" type="VARCHAR(255)"/>
            <column name="keycode" type="VARCHAR(255)"/>
            <column name="medicationDetails" type="CLOB"/>
            <column name="militarynumber" type="VARCHAR(255)"/>
            <column name="mothersfirstname" type="VARCHAR(255)"/>
            <column name="nhs" type="VARCHAR(10)"/>
            <column name="ni" type="VARCHAR(255)"/>
            <column name="paris" type="VARCHAR(255)"/>
            <column name="sexuality" type="VARCHAR(255)"/>
            <column name="crbnumber" type="VARCHAR(255)"/>
            <column name="disclosurenumber" type="VARCHAR(255)"/>
            <column name="enddate" type="DATETIME"/>
            <column name="startdate" type="DATETIME"/>
            <column name="workcapacity" type="INT"/>
            <column name="avatarid" type="BIGINT"/>
            <column name="commenttarget_id" type="BIGINT"/>
            <column name="contactsid" type="BIGINT"/>
            <column name="ethnicoriginsid" type="BIGINT"/>
            <column name="languagesid" type="BIGINT"/>
            <column name="religionsid" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-141">
        <createTable tableName="workers_jobs">
            <column name="entitlement" type="DECIMAL(19,2)"/>
            <column name="fromdate" type="DATETIME"/>
            <column name="rate" type="DECIMAL(19,2)"/>
            <column name="todate" type="DATETIME"/>
            <column name="version" type="INT"/>
            <column name="volunteer" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="workerid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="jobid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-142">
        <addPrimaryKey columnNames="activityid, clientid" tableName="activities_clients"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-143">
        <addPrimaryKey columnNames="activityid, workerid" tableName="activities_workers"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-144">
        <addPrimaryKey columnNames="contactid, eventid" tableName="contacts_events"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-145">
        <addPrimaryKey columnNames="collectionid, itemid" tableName="cosmo_collection_item"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-146">
        <addPrimaryKey columnNames="attributeid, keyname" tableName="cosmo_dictionary_values"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-147">
        <addPrimaryKey columnNames="ticketid, privilege" tableName="cosmo_ticket_privilege"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-150">
        <addPrimaryKey columnNames="hrid, outcomeId" tableName="hr_outcomes"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-151">
        <addPrimaryKey columnNames="outcomeId, referralaspectId, servicetypeId" tableName="outcomes_st_referralaspects"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-152">
        <addPrimaryKey columnNames="threatoutcomeId, supportplanactionid" tableName="outcomethreats_actionsupports"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-153">
        <addPrimaryKey columnNames="threatoutcomeId, questionId" tableName="outcomethreats_questions"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-154">
        <addPrimaryKey columnNames="questiongroupId, referralaspectId, servicetypeId" tableName="qg_st_referralaspects"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-155">
        <addPrimaryKey columnNames="questionId, questionanswerfreeId" tableName="questions_questionanswerfrees"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-156">
        <addPrimaryKey columnNames="questionId, questionanswerchoiceId" tableName="questions_questionanswrchoices"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-157">
        <addPrimaryKey columnNames="referralid, contactid" tableName="referrals_contacts"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-158">
        <addPrimaryKey columnNames="projectId, serviceId" tableName="services_projects"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-159">
        <addPrimaryKey columnNames="servicetypeId, flagId" tableName="servicetypes_flagthreats"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-160">
        <addPrimaryKey columnNames="servicetypeId, outcomeId" tableName="servicetypes_outcomeservices"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-161">
        <addPrimaryKey columnNames="servicetypeId, outcomeId" tableName="servicetypes_outcomesupports"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-162">
        <addPrimaryKey columnNames="servicetypeId, threatoutcomeId" tableName="servicetypes_outcomethreats"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-163">
        <addPrimaryKey columnNames="servicetypeId, questiongroupId" tableName="servicetypes_questiongroups"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-164">
        <addPrimaryKey columnNames="referralaspectId, servicetypeId" tableName="servicetypes_referralaspects"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-165">
        <addPrimaryKey columnNames="supporthrworkid, actionid" tableName="supporthrwork_actions"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-166">
        <addPrimaryKey columnNames="supportplanserviceworkid, actionid" tableName="supportplanservicework_actions"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-167">
        <addPrimaryKey columnNames="supportplanworkid, actionid" tableName="supportplanwork_actions"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-168">
        <addPrimaryKey columnNames="supportthreatworkid, actionid" tableName="supportthreatwork_actions"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-170">
        <addPrimaryKey columnNames="jobid, workerid" tableName="workers_jobs"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-407">
        <createIndex indexName="CONSTRAINT_INDEX_1" tableName="cosmo_attribute" unique="true">
            <column name="itemid"/>
            <column name="namespace"/>
            <column name="localname"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-408">
        <createIndex indexName="IDX_ATTRNAME" tableName="cosmo_attribute" unique="false">
            <column name="localname"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-409">
        <createIndex indexName="IDX_ATTRNS" tableName="cosmo_attribute" unique="false">
            <column name="namespace"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-410">
        <createIndex indexName="IDX_ATTRTYPE" tableName="cosmo_attribute" unique="false">
            <column name="attributetype"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-411">
        <createIndex indexName="IDX_ENDDT" tableName="cosmo_event_stamp" unique="false">
            <column name="enddate"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-412">
        <createIndex indexName="IDX_FLOATING" tableName="cosmo_event_stamp" unique="false">
            <column name="isfloating"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-413">
        <createIndex indexName="IDX_RECURRING" tableName="cosmo_event_stamp" unique="false">
            <column name="isrecurring"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-414">
        <createIndex indexName="IDX_STARTDT" tableName="cosmo_event_stamp" unique="false">
            <column name="startdate"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-416">
        <createIndex indexName="IDX_ITEMNAME" tableName="cosmo_item" unique="false">
            <column name="itemname"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-417">
        <createIndex indexName="IDX_ITEMTYPE" tableName="cosmo_item" unique="false">
            <column name="itemtype"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-419">
        <createIndex indexName="CONSTRAINT_INDEX_16" tableName="cosmo_pwrecovery" unique="true">
            <column name="pwrecoverykey"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-421">
        <createIndex indexName="CONSTRAINT_INDEX_9C" tableName="cosmo_server_properties" unique="true">
            <column name="propertyname"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-423">
        <createIndex indexName="CONSTRAINT_INDEX_A5" tableName="cosmo_stamp" unique="true">
            <column name="itemid"/>
            <column name="stamptype"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-424">
        <createIndex indexName="IDX_STAMPTYPE" tableName="cosmo_stamp" unique="false">
            <column name="stamptype"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-425">
        <createIndex indexName="CONSTRAINT_INDEX_5" tableName="cosmo_subscription" unique="true">
            <column name="ownerid"/>
            <column name="displayname"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-426">
        <createIndex indexName="CONSTRAINT_INDEX_B" tableName="cosmo_tickets" unique="true">
            <column name="ticketkey"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-428">
        <createIndex indexName="CONSTRAINT_INDEX_C" tableName="cosmo_user_preferences" unique="true">
            <column name="userid"/>
            <column name="preferencename"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-429">
        <createIndex indexName="CONSTRAINT_INDEX_A56" tableName="cosmo_users" unique="true">
            <column name="email"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-430">
        <createIndex indexName="CONSTRAINT_INDEX_A56C" tableName="cosmo_users" unique="true">
            <column name="user_uid"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-431">
        <createIndex indexName="CONSTRAINT_INDEX_A56C6" tableName="cosmo_users" unique="true">
            <column name="username"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-432">
        <createIndex indexName="IDX_ACTIVATIONID" tableName="cosmo_users" unique="false">
            <column name="activationid"/>
        </createIndex>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-171">
        <addForeignKeyConstraint baseColumnNames="projectId" baseTableName="accommodations" constraintName="FKDFA58A61D13AA2AA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="projects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-172">
        <addForeignKeyConstraint baseColumnNames="riskId" baseTableName="actionfakes" constraintName="FK5EC00748192EB67C" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="risks" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-173">
        <addForeignKeyConstraint baseColumnNames="action1_id" baseTableName="actionlinks" constraintName="FK5F1843431A4F2018" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-174">
        <addForeignKeyConstraint baseColumnNames="action2_id" baseTableName="actionlinks" constraintName="FK5F1843431A4F9477" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-175">
        <addForeignKeyConstraint baseColumnNames="riskId" baseTableName="actions" constraintName="FKBAC048FD192EB67C" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="risks" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-176">
        <addForeignKeyConstraint baseColumnNames="activitytypeid" baseTableName="activities" constraintName="FK7A1B3BED661234CB" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="activitytypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-177">
        <addForeignKeyConstraint baseColumnNames="venueid" baseTableName="activities" constraintName="FK7A1B3BED88F2AFBB" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="venues" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-178">
        <addForeignKeyConstraint baseColumnNames="activityid" baseTableName="activities_clients" constraintName="FKF7E62BB6C300B6B7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="activities" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-179">
        <addForeignKeyConstraint baseColumnNames="clientid" baseTableName="activities_clients" constraintName="FKF7E62BB6DF5306A5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="clientdetails" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-180">
        <addForeignKeyConstraint baseColumnNames="activityid" baseTableName="activities_workers" constraintName="FK1F82C263C300B6B7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="activities" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-181">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="activities_workers" constraintName="FK1F82C263FF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-182">
        <addForeignKeyConstraint baseColumnNames="serviceId" baseTableName="activitytypes" constraintName="FKC367F6CA83B122E2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-183">
        <addForeignKeyConstraint baseColumnNames="usersid" baseTableName="audits" constraintName="FKAC25DFF8A188AAF9" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-185">
        <addForeignKeyConstraint baseColumnNames="avatarid" baseTableName="clientdetails" constraintName="FK12AD341774F70944" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="uploadbytes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-187">
        <addForeignKeyConstraint baseColumnNames="contactsid" baseTableName="clientdetails" constraintName="FK12AD341778F3A48A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-188">
        <addForeignKeyConstraint baseColumnNames="ethnicoriginsid" baseTableName="clientdetails" constraintName="FK12AD3417A88FE951" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="ethnicorigins" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-189">
        <addForeignKeyConstraint baseColumnNames="languagesid" baseTableName="clientdetails" constraintName="FK12AD341760C8AFD1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="languages" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-190">
        <addForeignKeyConstraint baseColumnNames="religionsid" baseTableName="clientdetails" constraintName="FK12AD3417BF94B531" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="religions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-193">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="commenttypes" constraintName="FK30968D7A430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-194">
        <addForeignKeyConstraint baseColumnNames="agencycategoryid" baseTableName="contacts" constraintName="FKDE2D6053D03E8844" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="agencycategories" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-195">
        <addForeignKeyConstraint baseColumnNames="companyid" baseTableName="contacts" constraintName="FKDE2D6053A6C153D5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-196">
        <addForeignKeyConstraint baseColumnNames="economicstatusid" baseTableName="contacts" constraintName="FKDE2D6053E597978C" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="economicstatuses" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-197">
        <addForeignKeyConstraint baseColumnNames="usersid" baseTableName="contacts" constraintName="FKDE2D6053A188AAF9" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-198">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="contacts_events" constraintName="FKB8800A85E275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-199">
        <addForeignKeyConstraint baseColumnNames="eventid" baseTableName="contacts_events" constraintName="FKB8800A854A49053B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="events" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-200">
        <addForeignKeyConstraint baseColumnNames="itemid" baseTableName="cosmo_attribute" constraintName="FK51B5FF46ACA5401" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_item" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-201">
        <addForeignKeyConstraint baseColumnNames="collectionid" baseTableName="cosmo_collection_item" constraintName="FK51F3A53E4C54948A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_item" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-202">
        <addForeignKeyConstraint baseColumnNames="itemid" baseTableName="cosmo_collection_item" constraintName="FK51F3A53EACA5401" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_item" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-203">
        <addForeignKeyConstraint baseColumnNames="attributeid" baseTableName="cosmo_dictionary_values" constraintName="FKD06E7F95CB6C29DD" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_attribute" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-204">
        <addForeignKeyConstraint baseColumnNames="stampid" baseTableName="cosmo_event_stamp" constraintName="FKE97B9008219E1898" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_stamp" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-205">
        <addForeignKeyConstraint baseColumnNames="contentdataid" baseTableName="cosmo_item" constraintName="FKB517642967232A41" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_content_data" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-206">
        <addForeignKeyConstraint baseColumnNames="modifiesitemid" baseTableName="cosmo_item" constraintName="FKB5176429F9D9EFEB" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_item" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-207">
        <addForeignKeyConstraint baseColumnNames="ownerid" baseTableName="cosmo_item" constraintName="FKB51764291232D459" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_users" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-208">
        <addForeignKeyConstraint baseColumnNames="attributeid" baseTableName="cosmo_multistring_values" constraintName="FK540AAF0DEC6BDEA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_attribute" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-209">
        <addForeignKeyConstraint baseColumnNames="userid" baseTableName="cosmo_pwrecovery" constraintName="FK692BD7321F3BE771" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_users" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-210">
        <addForeignKeyConstraint baseColumnNames="itemid" baseTableName="cosmo_stamp" constraintName="FKEE61FD6DACA5401" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_item" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-211">
        <addForeignKeyConstraint baseColumnNames="ownerid" baseTableName="cosmo_subscription" constraintName="FK1B24CC331232D459" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_users" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-212">
        <addForeignKeyConstraint baseColumnNames="ticketid" baseTableName="cosmo_ticket_privilege" constraintName="FK2A25F554C0EFB9B3" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_tickets" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-213">
        <addForeignKeyConstraint baseColumnNames="itemid" baseTableName="cosmo_tickets" constraintName="FK146E11ACA5401" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_item" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-214">
        <addForeignKeyConstraint baseColumnNames="ownerid" baseTableName="cosmo_tickets" constraintName="FK146E111232D459" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_users" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-215">
        <addForeignKeyConstraint baseColumnNames="itemid" baseTableName="cosmo_tombstones" constraintName="FKDA68294ACA5401" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_item" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-216">
        <addForeignKeyConstraint baseColumnNames="userid" baseTableName="cosmo_user_preferences" constraintName="FK5F2EC89A1F3BE771" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="cosmo_users" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-217">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="events" constraintName="FKB307E1194946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-218">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="events" constraintName="FKB307E1192DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-219">
        <addForeignKeyConstraint baseColumnNames="serviceId" baseTableName="events" constraintName="FKB307E11983B122E2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-220">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="events" constraintName="FKB307E119FF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-225">
        <addForeignKeyConstraint baseColumnNames="hrid" baseTableName="hr_outcomes" constraintName="FK4E7712F6C5D82930" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="hr" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-226">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="hr_outcomes" constraintName="FK4E7712F64233F0DC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-227">
        <addForeignKeyConstraint baseColumnNames="leavereasonid" baseTableName="hrfromtos" constraintName="FK5AA6A5247250D290" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="leavereasons" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-228">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="hrfromtos" constraintName="FK5AA6A524FF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-229">
        <addForeignKeyConstraint baseColumnNames="workerid, jobid" baseTableName="hrfromtos" constraintName="FK5AA6A5245288E98E" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="jobid, workerid" referencedTableName="workers_jobs" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-230">
        <addForeignKeyConstraint baseColumnNames="workerjob_jobid, workerjob_workerid" baseTableName="hrfromtos" constraintName="FK5AA6A52442A6A18E" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="jobid, workerid" referencedTableName="workers_jobs" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-231">
        <addForeignKeyConstraint baseColumnNames="hrid" baseTableName="hrsettings" constraintName="FK99D21F2DC5D82930" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="hr" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-232">
        <addForeignKeyConstraint baseColumnNames="individualId" baseTableName="individual_individualTypes" constraintName="FK884623BA14FC6D50" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-233">
        <addForeignKeyConstraint baseColumnNames="departmentid" baseTableName="jobs" constraintName="FK31DC565BE012C0" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="departments" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-235">
        <addForeignKeyConstraint baseColumnNames="questiongroupId" baseTableName="outcomes" constraintName="FK374E361E24A574A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questiongroups" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-236">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="outcomes_st_referralaspects" constraintName="FKC8F179A138BA1BCD" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-237">
        <addForeignKeyConstraint baseColumnNames="referralaspectId, servicetypeId" baseTableName="outcomes_st_referralaspects" constraintName="FKC8F179A12FD0C742" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="referralaspectId, servicetypeId" referencedTableName="servicetypes_referralaspects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-238">
        <addForeignKeyConstraint baseColumnNames="supportplanactionid" baseTableName="outcomethreats_actionsupports" constraintName="FK3C76F3F28F545E42" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-239">
        <addForeignKeyConstraint baseColumnNames="threatoutcomeId" baseTableName="outcomethreats_actionsupports" constraintName="FK3C76F3F21E616AFC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-240">
        <addForeignKeyConstraint baseColumnNames="questionId" baseTableName="outcomethreats_questions" constraintName="FKDBB4CD15E470F56A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-241">
        <addForeignKeyConstraint baseColumnNames="threatoutcomeId" baseTableName="outcomethreats_questions" constraintName="FKDBB4CD151E616AFC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-242">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="projectcomments" constraintName="FKC0DC000D581BA917" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-243">
        <addForeignKeyConstraint baseColumnNames="projectId" baseTableName="projectcomments" constraintName="FKC0DC000DD13AA2AA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="projects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-244">
        <addForeignKeyConstraint baseColumnNames="typeid" baseTableName="projectcomments" constraintName="FKC0DC000D42BDAB2B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="commenttypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-245">
        <addForeignKeyConstraint baseColumnNames="regionId" baseTableName="projects" constraintName="FKC479187A9FF0E4C6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="regions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-246">
        <addForeignKeyConstraint baseColumnNames="questiongroupId" baseTableName="qg_st_referralaspects" constraintName="FK4EADE66839EE860D" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questiongroups" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-247">
        <addForeignKeyConstraint baseColumnNames="referralaspectId, servicetypeId" baseTableName="qg_st_referralaspects" constraintName="FK4EADE6682FD0C742" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="referralaspectId, servicetypeId" referencedTableName="servicetypes_referralaspects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-248">
        <addForeignKeyConstraint baseColumnNames="questiongroupId" baseTableName="questiongroups_questions" constraintName="FK49368C88E24A574A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questiongroups" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-249">
        <addForeignKeyConstraint baseColumnNames="questionId" baseTableName="questiongroups_questions" constraintName="FK49368C88E470F56A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-250">
        <addForeignKeyConstraint baseColumnNames="questionanswerfreeId" baseTableName="questions_questionanswerfrees" constraintName="FK5F713CF1C940DABE" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questionanswerfrees" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-251">
        <addForeignKeyConstraint baseColumnNames="questionId" baseTableName="questions_questionanswerfrees" constraintName="FK5F713CF1E470F56A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-252">
        <addForeignKeyConstraint baseColumnNames="questionanswerchoiceId" baseTableName="questions_questionanswrchoices" constraintName="FK98E129DC3F72A928" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questionanswerchoices" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-253">
        <addForeignKeyConstraint baseColumnNames="questionId" baseTableName="questions_questionanswrchoices" constraintName="FK98E129DCE470F56A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-254">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="referralactivities" constraintName="FKAFAD974A4946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-255">
        <addForeignKeyConstraint baseColumnNames="activitytypeid" baseTableName="referralactivities" constraintName="FKAFAD974A444AEA4D" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referralactivitytypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-256">
        <addForeignKeyConstraint baseColumnNames="agencyid" baseTableName="referralactivities" constraintName="FKAFAD974A3B9CEA8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-257">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="referralactivities" constraintName="FKAFAD974A2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-258">
        <addForeignKeyConstraint baseColumnNames="supportplanworkid" baseTableName="referralactivities" constraintName="FKAFAD974AAFF94736" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-259">
        <addForeignKeyConstraint baseColumnNames="referralactivityid" baseTableName="referralactivityworker" constraintName="FKD234E42AD2F6D896" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referralactivities" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-260">
        <addForeignKeyConstraint baseColumnNames="supportworkerid" baseTableName="referralactivityworker" constraintName="FKD234E42A4891B9C4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-261">
        <addForeignKeyConstraint baseColumnNames="bytesid" baseTableName="referralattachments" constraintName="FK50ACDBF31F9E8BD6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="uploadbytes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-262">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="referralattachments" constraintName="FK50ACDBF32DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-263">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="referralcomments" constraintName="FKB2536C91581BA917" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-264">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="referralcomments" constraintName="FKB2536C912DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-265">
        <addForeignKeyConstraint baseColumnNames="typeid" baseTableName="referralcomments" constraintName="FKB2536C9142BDAB2B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="commenttypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-266">
        <addForeignKeyConstraint baseColumnNames="accommodationid" baseTableName="referralprojects" constraintName="FK9AB4A717F15C391C" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="accommodations" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-267">
        <addForeignKeyConstraint baseColumnNames="projectId" baseTableName="referralprojects" constraintName="FK9AB4A717D13AA2AA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="projects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-268">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="referralprojects" constraintName="FK9AB4A7172DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-269">
        <addForeignKeyConstraint baseColumnNames="accommodationcategoryid" baseTableName="referrals" constraintName="FKC8E0F87680D93EB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="accommodationCategories" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-270">
        <addForeignKeyConstraint baseColumnNames="agencyid" baseTableName="referrals" constraintName="FKC8E0F8763B9CEA8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-271">
        <addForeignKeyConstraint baseColumnNames="clientid" baseTableName="referrals" constraintName="FKC8E0F876DF5306A5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="clientdetails" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-272">
        <addForeignKeyConstraint baseColumnNames="deliveredbyid" baseTableName="referrals" constraintName="FKC8E0F876770070FE" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-273">
        <addForeignKeyConstraint baseColumnNames="exitcommentid" baseTableName="referrals" constraintName="FKC8E0F876D61BC2DA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referralcomments" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-274">
        <addForeignKeyConstraint baseColumnNames="exitreasonid" baseTableName="referrals" constraintName="FKC8E0F8767C5B9FC2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="exitreasons" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-275">
        <addForeignKeyConstraint baseColumnNames="fundingsourceid" baseTableName="referrals" constraintName="FKC8E0F8764AA5E33B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-276">
        <addForeignKeyConstraint baseColumnNames="interviewer1id" baseTableName="referrals" constraintName="FKC8E0F87636DFD43A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-277">
        <addForeignKeyConstraint baseColumnNames="interviewer2id" baseTableName="referrals" constraintName="FKC8E0F87636DFD7FB" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-278">
        <addForeignKeyConstraint baseColumnNames="offenceid" baseTableName="referrals" constraintName="FKC8E0F876306509AC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="offences" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-279">
        <addForeignKeyConstraint baseColumnNames="pendingstatusid" baseTableName="referrals" constraintName="FKC8E0F87695A1B6A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pendingstatuses" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-280">
        <addForeignKeyConstraint baseColumnNames="primaryreferralid" baseTableName="referrals" constraintName="FKC8E0F8761E3BB9F5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-281">
        <addForeignKeyConstraint baseColumnNames="projectpreferredid" baseTableName="referrals" constraintName="FKC8E0F876421AA559" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="projects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-282">
        <addForeignKeyConstraint baseColumnNames="referredserviceid" baseTableName="referrals" constraintName="FKC8E0F876262EA9F1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-283">
        <addForeignKeyConstraint baseColumnNames="referrerid" baseTableName="referrals" constraintName="FKC8E0F876C3DCFD56" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-284">
        <addForeignKeyConstraint baseColumnNames="signpostedagencyid" baseTableName="referrals" constraintName="FKC8E0F87638E1ABE4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-285">
        <addForeignKeyConstraint baseColumnNames="signpostedcommentid" baseTableName="referrals" constraintName="FKC8E0F876AADF325D" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referralcomments" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-286">
        <addForeignKeyConstraint baseColumnNames="signpostedparentreferralid" baseTableName="referrals" constraintName="FKC8E0F876501363D9" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-287">
        <addForeignKeyConstraint baseColumnNames="signpostedreasonid" baseTableName="referrals" constraintName="FKC8E0F8768B7AB3DF" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="signpostreasons" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-288">
        <addForeignKeyConstraint baseColumnNames="signpostedserviceid" baseTableName="referrals" constraintName="FKC8E0F876F384ED26" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-289">
        <addForeignKeyConstraint baseColumnNames="sourceregionid" baseTableName="referrals" constraintName="FKC8E0F8764AD101E1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="regions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-290">
        <addForeignKeyConstraint baseColumnNames="sourcetypeid" baseTableName="referrals" constraintName="FKC8E0F876DC9DAB67" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrersources" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-291">
        <addForeignKeyConstraint baseColumnNames="supportworkerid" baseTableName="referrals" constraintName="FKC8E0F8764891B9C4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-292">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="referrals_contacts" constraintName="FKD622BDFC581BA917" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-293">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="referrals_contacts" constraintName="FKD622BDFC2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-294">
        <addForeignKeyConstraint baseColumnNames="bytesid" baseTableName="reportattachments" constraintName="FKB025EDFC1F9E8BD6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="uploadbytes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-295">
        <addForeignKeyConstraint baseColumnNames="reportid" baseTableName="reportattachments" constraintName="FKB025EDFC40A30D55" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="reports" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-296">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="reviews" constraintName="FK418FF41B2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-297">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="risks" constraintName="FK677EF844233F0DC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-298">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="services" constraintName="FK5235105E430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-299">
        <addForeignKeyConstraint baseColumnNames="projectId" baseTableName="services_projects" constraintName="FK6D52C33BD13AA2AA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="projects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-300">
        <addForeignKeyConstraint baseColumnNames="serviceId" baseTableName="services_projects" constraintName="FK6D52C33B83B122E2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-301">
        <addForeignKeyConstraint baseColumnNames="flagId" baseTableName="servicetypes_flagthreats" constraintName="FKEB3A67D24D12C76" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="flags" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-302">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="servicetypes_flagthreats" constraintName="FKEB3A67D2430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-303">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="servicetypes_outcomeservices" constraintName="FKF80D2E551D8B62B3" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-304">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="servicetypes_outcomeservices" constraintName="FKF80D2E55430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-305">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="servicetypes_outcomesupports" constraintName="FK42B5987B38BA1BCD" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-306">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="servicetypes_outcomesupports" constraintName="FK42B5987B430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-307">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="servicetypes_outcomethreats" constraintName="FK52817CC2430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-308">
        <addForeignKeyConstraint baseColumnNames="threatoutcomeId" baseTableName="servicetypes_outcomethreats" constraintName="FK52817CC21E616AFC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-309">
        <addForeignKeyConstraint baseColumnNames="questiongroupId" baseTableName="servicetypes_questiongroups" constraintName="FK35335FF539EE860D" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questiongroups" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-310">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="servicetypes_questiongroups" constraintName="FK35335FF5430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-311">
        <addForeignKeyConstraint baseColumnNames="referralaspectId" baseTableName="servicetypes_referralaspects" constraintName="FK75A4A983F87C9028" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referralaspects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-312">
        <addForeignKeyConstraint baseColumnNames="servicetypeId" baseTableName="servicetypes_referralaspects" constraintName="FK75A4A983430A9376" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="servicetypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-313">
        <addForeignKeyConstraint baseColumnNames="individualId" baseTableName="signature" constraintName="FK3FFD98B814FC6D50" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-314">
        <addForeignKeyConstraint baseColumnNames="referralaspectId, servicetypeId" baseTableName="st_referralaspectsettings" constraintName="FK2410C1132FD0C742" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="referralaspectId, servicetypeId" referencedTableName="servicetypes_referralaspects" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-315">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supporthractions" constraintName="FK1E2235C44946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-316">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supporthractions" constraintName="FK1E2235C4E275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-317">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="supporthractions" constraintName="FK1E2235C4FF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-318">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supporthractions" constraintName="FK1E2235C4F50BA597" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supporthrwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-319">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supporthrcomments" constraintName="FKE4F38A0D581BA917" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-320">
        <addForeignKeyConstraint baseColumnNames="typeid" baseTableName="supporthrcomments" constraintName="FKE4F38A0D42BDAB2B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="commenttypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-321">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="supporthrcomments" constraintName="FKE4F38A0DFF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-322">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supporthrcomments" constraintName="FKE4F38A0DF50BA597" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supporthrwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-323">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supporthroutcomes" constraintName="FKC508F7AE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-324">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="supporthroutcomes" constraintName="FKC508F7A4233F0DC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-325">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="supporthroutcomes" constraintName="FKC508F7AFF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-326">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supporthroutcomes" constraintName="FKC508F7AF50BA597" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supporthrwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-327">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supporthrwork" constraintName="FKE97767AAE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-328">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="supporthrwork" constraintName="FKE97767AAFF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-329">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supporthrwork_actions" constraintName="FKC7E20D684946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-330">
        <addForeignKeyConstraint baseColumnNames="supporthrworkid" baseTableName="supporthrwork_actions" constraintName="FKC7E20D688CE583F0" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supporthrwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-331">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supportplanactions" constraintName="FK8B7666854946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-332">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanactions" constraintName="FK8B766685E275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-333">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportplanactions" constraintName="FK8B7666852DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-334">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplanactions" constraintName="FK8B766685672D35BE" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-335">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplananswers" constraintName="FK9E33D5FDE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-336">
        <addForeignKeyConstraint baseColumnNames="questionId" baseTableName="supportplananswers" constraintName="FK9E33D5FDE470F56A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="questions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-337">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportplananswers" constraintName="FK9E33D5FD2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-338">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplananswers" constraintName="FK9E33D5FD672D35BE" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-339">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplancomments" constraintName="FK2225716C581BA917" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-340">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportplancomments" constraintName="FK2225716C2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-341">
        <addForeignKeyConstraint baseColumnNames="threatworkid" baseTableName="supportplancomments" constraintName="FK2225716CB62BDA3F" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportthreatwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-342">
        <addForeignKeyConstraint baseColumnNames="typeid" baseTableName="supportplancomments" constraintName="FK2225716C42BDAB2B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="commenttypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-343">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplancomments" constraintName="FK2225716C672D35BE" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-344">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanoutcomes" constraintName="FK498276D9E275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-345">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="supportplanoutcomes" constraintName="FK498276D94233F0DC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-346">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportplanoutcomes" constraintName="FK498276D92DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-347">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplanoutcomes" constraintName="FK498276D9672D35BE" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-348">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanrisks" constraintName="FK63A7CF0CE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-349">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportplanrisks" constraintName="FK63A7CF0C2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-350">
        <addForeignKeyConstraint baseColumnNames="riskId" baseTableName="supportplanrisks" constraintName="FK63A7CF0C192EB67C" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="risks" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-351">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplanrisks" constraintName="FK63A7CF0C672D35BE" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-352">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supportplanserviceactions" constraintName="FKA64596204946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-353">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanserviceactions" constraintName="FKA6459620E275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-354">
        <addForeignKeyConstraint baseColumnNames="serviceId" baseTableName="supportplanserviceactions" constraintName="FKA645962083B122E2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-355">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplanserviceactions" constraintName="FKA6459620629499A4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanservicework" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-356">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanservicecomments" constraintName="FK613C3531581BA917" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-357">
        <addForeignKeyConstraint baseColumnNames="serviceId" baseTableName="supportplanservicecomments" constraintName="FK613C353183B122E2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-358">
        <addForeignKeyConstraint baseColumnNames="typeid" baseTableName="supportplanservicecomments" constraintName="FK613C353142BDAB2B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="commenttypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-359">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplanservicecomments" constraintName="FK613C3531629499A4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanservicework" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-360">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanserviceoutcomes" constraintName="FK88993A9EE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-361">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="supportplanserviceoutcomes" constraintName="FK88993A9E4233F0DC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-362">
        <addForeignKeyConstraint baseColumnNames="serviceId" baseTableName="supportplanserviceoutcomes" constraintName="FK88993A9E83B122E2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-363">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportplanserviceoutcomes" constraintName="FK88993A9E629499A4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanservicework" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-364">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanservicework" constraintName="FKB2FB44CEE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-365">
        <addForeignKeyConstraint baseColumnNames="serviceId" baseTableName="supportplanservicework" constraintName="FKB2FB44CE83B122E2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="services" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-366">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supportplanservicework_actions" constraintName="FK4BD8868C4946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-367">
        <addForeignKeyConstraint baseColumnNames="supportplanserviceworkid" baseTableName="supportplanservicework_actions" constraintName="FK4BD8868C726F9C21" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanservicework" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-368">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportplanwork" constraintName="FK4549D689E275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-369">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportplanwork" constraintName="FK4549D6892DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-370">
        <addForeignKeyConstraint baseColumnNames="reviewid" baseTableName="supportplanwork" constraintName="FK4549D689B98BFC8E" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="reviews" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-371">
        <addForeignKeyConstraint baseColumnNames="signatureid" baseTableName="supportplanwork" constraintName="FK4549D68951665EE8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="signature" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-372">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supportplanwork_actions" constraintName="FKC1032D474946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-373">
        <addForeignKeyConstraint baseColumnNames="supportplanworkid" baseTableName="supportplanwork_actions" constraintName="FKC1032D47AFF94736" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportplanwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-374">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supportthreatactions" constraintName="FK5E3BF2344946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-375">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportthreatactions" constraintName="FK5E3BF234E275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-376">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportthreatactions" constraintName="FK5E3BF2342DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-377">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportthreatactions" constraintName="FK5E3BF234206B47A5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportthreatwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-378">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportthreatcomments" constraintName="FKA8115B9D581BA917" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-379">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportthreatcomments" constraintName="FKA8115B9D2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-380">
        <addForeignKeyConstraint baseColumnNames="typeid" baseTableName="supportthreatcomments" constraintName="FKA8115B9D42BDAB2B" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="commenttypes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-381">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportthreatcomments" constraintName="FKA8115B9D206B47A5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportthreatwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-382">
        <addForeignKeyConstraint baseColumnNames="flagId" baseTableName="supportthreatflags" constraintName="FK5BC2797E4D12C76" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="flags" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-383">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportthreatflags" constraintName="FK5BC2797E2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-384">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportthreatflags" constraintName="FK5BC2797E206B47A5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportthreatwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-385">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportthreatoutcomes" constraintName="FKCF6E610AE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-386">
        <addForeignKeyConstraint baseColumnNames="outcomeId" baseTableName="supportthreatoutcomes" constraintName="FKCF6E610A4233F0DC" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-387">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportthreatoutcomes" constraintName="FKCF6E610A2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-388">
        <addForeignKeyConstraint baseColumnNames="workid" baseTableName="supportthreatoutcomes" constraintName="FKCF6E610A206B47A5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportthreatwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-389">
        <addForeignKeyConstraint baseColumnNames="contactid" baseTableName="supportthreatwork" constraintName="FK7EDC813AE275DEB8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-390">
        <addForeignKeyConstraint baseColumnNames="referralid" baseTableName="supportthreatwork" constraintName="FK7EDC813A2DAEC673" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="referrals" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-391">
        <addForeignKeyConstraint baseColumnNames="actionid" baseTableName="supportthreatwork_actions" constraintName="FK3D9C96F84946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-392">
        <addForeignKeyConstraint baseColumnNames="supportthreatworkid" baseTableName="supportthreatwork_actions" constraintName="FK3D9C96F888CA1B8E" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supportthreatwork" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-393">
        <addForeignKeyConstraint baseColumnNames="bytesid" baseTableName="uploadfile" constraintName="FKF18B1AFD1F9E8BD6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="uploadbytes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-395">
        <addForeignKeyConstraint baseColumnNames="jobid, workerid" baseTableName="work" constraintName="FK37C7115288E98E" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="jobid, workerid" referencedTableName="workers_jobs" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-396">
        <addForeignKeyConstraint baseColumnNames="bytesid" baseTableName="workerattachments" constraintName="FK135EFD721F9E8BD6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="uploadbytes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-397">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="workerattachments" constraintName="FK135EFD72FF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-398">
        <addForeignKeyConstraint baseColumnNames="avatarid" baseTableName="workers" constraintName="FK5AE81CB574F70944" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="uploadbytes" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-400">
        <addForeignKeyConstraint baseColumnNames="contactsid" baseTableName="workers" constraintName="FK5AE81CB578F3A48A" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-401">
        <addForeignKeyConstraint baseColumnNames="ethnicoriginsid" baseTableName="workers" constraintName="FK5AE81CB5A88FE951" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="ethnicorigins" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-402">
        <addForeignKeyConstraint baseColumnNames="languagesid" baseTableName="workers" constraintName="FK5AE81CB560C8AFD1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="languages" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-403">
        <addForeignKeyConstraint baseColumnNames="religionsid" baseTableName="workers" constraintName="FK5AE81CB5BF94B531" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="religions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-404">
        <addForeignKeyConstraint baseColumnNames="jobid" baseTableName="workers_jobs" constraintName="FK381321A0F547BCF4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="jobs" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-405">
        <addForeignKeyConstraint baseColumnNames="workerid" baseTableName="workers_jobs" constraintName="FK381321A0FF50C098" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="workers" referencesUniqueColumn="false"/>
    </changeSet>
</databaseChangeLog>
