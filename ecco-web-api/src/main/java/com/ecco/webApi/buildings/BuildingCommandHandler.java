package com.ecco.webApi.buildings;

import com.ecco.service.AddressService;
import com.ecco.webApi.CommandResult;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import com.ecco.webApi.contacts.address.AddressedLocationFromViewModel;
import com.ecco.webApi.contacts.address.AddressedLocationToViewModel;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.Authentication;

import com.ecco.buildings.dom.BuildingCommand;
import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.BuildingsCommandRepository;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Objects;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@Component
public class BuildingCommandHandler
    extends BaseCommandHandler<BuildingCommandViewModel, Integer, BuildingCommand, @Nullable Void> { //params is actually null

    private final FixedContainerRepository bldgRepository;
    private final BuildingMapper buildingMapper;
    private final AddressService addressService;

    public BuildingCommandHandler(ObjectMapper objectMapper,
                                  BuildingsCommandRepository commandRepository,
                                  BuildingMapper buildingMapper,
                                  FixedContainerRepository bldgRepository,
                                  AddressService addressService) {
        super(objectMapper, commandRepository, BuildingCommandViewModel.class);
        this.bldgRepository = bldgRepository;
        this.buildingMapper = buildingMapper;
        this.addressService = addressService;
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, @Nullable Void dontUse, @NotNull BuildingCommandViewModel viewModel) {
        var addressFromViewModel = new AddressedLocationFromViewModel();
        var addressToViewModel = new AddressedLocationToViewModel();

        FixedContainer bldg = (viewModel.serviceRecipientId == null)
            ? new FixedContainer()
            : bldgRepository.findByServiceRecipient_Id(viewModel.serviceRecipientId).orElseThrow(NullPointerException::new);

        buildingMapper.updateBuildingFromViewModel(viewModel, bldg);

        if (viewModel.parentId != null) {
            bldg.setParentId(viewModel.parentId.to);
            var parent = bldgRepository.findById(viewModel.parentId.to).get();
            // use the parent to create an address - merging the room with the parent line 1
            if (parent.getLocation() != null && viewModel.name != null) {
                var childAddress = addressToViewModel.apply(parent.getLocation());
                childAddress.address[0] = viewModel.name.to.concat(", ").concat(childAddress.address[0]);
                var adr = addressService.ensureAddress(Objects.requireNonNull(addressFromViewModel.apply(childAddress)));
                bldg.setLocationId(adr.getId());
            }
        }

        if (viewModel.address != null) {
            var adr = addressService.ensureAddress(Objects.requireNonNull(addressFromViewModel.apply(viewModel.address.to)));
            bldg.setLocationId(adr.getId());
        }

        bldgRepository.save(bldg);

        return CommandResult.ofLink(linkToApi(methodOn(BuildingController.class).findOne(bldg.getId())).withSelfRel())
                .withTargetId(bldg.getId());
    }

    @NonNull
    @Override
    protected BuildingCommand createCommand(Serializable targetId, @Nullable Void dontUse, @NonNull String requestBody, @NonNull BuildingCommandViewModel viewModel,
                                            long userId) {
        return new BuildingCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, viewModel.serviceRecipientId);
    }
}
