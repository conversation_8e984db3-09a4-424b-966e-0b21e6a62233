package com.ecco.webApi.messaging;

import com.ecco.dao.commands.ContactsCommandRepository;
import com.ecco.dom.commands.ContactMessageCommand;
import com.ecco.messaging.MessageService;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.contacts.ContactBaseCommandHandler;
import com.ecco.webApi.contacts.ContactCommandParams;
import com.ecco.webApi.controllers.NotFoundException;
import com.ecco.webApi.users.UserBaseController;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.joda.time.Instant;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.UUID;

@Component
public class ContactMessageCommandHandler extends ContactBaseCommandHandler<
        ContactMessageCommandDto, ContactMessageCommand, @NonNull ContactCommandParams> {

    private final MessageService sender;

    private final ContactRepository contactRepository;

    @Autowired
    public ContactMessageCommandHandler(@NonNull ObjectMapper objectMapper,
                                        @NonNull MessageService sender,
                                        @NonNull ContactsCommandRepository contactsCommandRepository,
                                        @NonNull ContactRepository contactRepository) {
        super(objectMapper, contactsCommandRepository, ContactMessageCommandDto.class);
        this.sender = sender;
        this.contactRepository = contactRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth,
                                           @NonNull ContactCommandParams params,
                                           @NonNull ContactMessageCommandDto viewModel) {

        var contact = contactRepository.findById((long) params.contactId).orElseThrow(() -> new NotFoundException("Not found"));
        var msg = sender.sendSms(contact.getMobileNumber(), viewModel.messageBody);
        // TODO ? apply NotFoundException(msg.message);
        // TODO check for what we did response, eg "SMS submitted" from TwilioMessageService
        return new CommandResult().withMessage(msg.isSuccessful() ? "message sent" : msg.getMessage());
    }

    @Override
    @NonNull
    protected ContactMessageCommand createCommand(Serializable targetId,
                                                  @NonNull ContactCommandParams params,
                                                  @NonNull String requestBody,
                                                  @NonNull ContactMessageCommandDto viewModel,
                                                  long userId) {
        Assert.state(params.contactId == viewModel.contactId, "contactId in body must match URI");

        return new ContactMessageCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.contactId);
    }

    @SneakyThrows
    public void saveInboundSms(int contactId, String message) {
        var dto = new ContactMessageCommandDto(contactId, message);
        var jsonBody = objectMapper.writeValueAsString(dto);
        var command = new ContactMessageCommand(UUID.randomUUID(), Instant.now(), UserBaseController.EXTERNAL_USERID,
                jsonBody, contactId);
        commandRepository.save(command);
    }
}
