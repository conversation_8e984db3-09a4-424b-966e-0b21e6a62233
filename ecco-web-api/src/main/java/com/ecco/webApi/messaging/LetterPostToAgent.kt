package com.ecco.webApi.messaging

import com.ecco.config.service.SoftwareFeatureService
import com.ecco.dom.commands.ReferralTaskUpdateCommand
import com.ecco.dom.commands.ServiceRecipientTaskCommand
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.infrastructure.bus.MessageBus
import com.ecco.serviceConfig.service.ServiceTypeService
import com.ecco.webApi.evidence.CommandCreatedEvent
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEvent
import org.springframework.stereotype.Component
import org.springframework.util.Assert
import org.springframework.util.StringUtils
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder
import java.util.concurrent.ExecutorService
import javax.annotation.PostConstruct

/**
 * A CommandCreatedEvent handler which will post events to a letter webhook
 */
@Component
class LetterPostToAgent(
    private val serviceRecipientRepository: ServiceRecipientRepository,
    private val serviceTypeService: ServiceTypeService,
    private val singleThreadExecutor: ExecutorService,
    private val messageBus: MessageBus<ApplicationEvent>,
    private val softwareFeatureService: SoftwareFeatureService,
    private val objectMapper: ObjectMapper,
) {
    private val log = LoggerFactory.getLogger(javaClass)

    private val restTemplate = RestTemplate()

    @Value("\${micro.eccoLetterUrl:}")
    var eccoLetterUrl: String? = null

    @PostConstruct
    protected fun init() {
        messageBus.subscribe(CommandCreatedEvent::class.java) { singleThreadExecutor.submit { this.syncEvent(it) } }
    }

    private fun syncEvent(event: CommandCreatedEvent) {
        if (!softwareFeatureService.featureEnabled("tasks.edit.new.triggerLetter")) {
            return
        }
        if (!StringUtils.hasText(eccoLetterUrl)) {
            return
        }

        // see SmsNotificationAgent
        if (event.command is ServiceRecipientTaskCommand) {
            // is NOT a task update
            if (event.command is ReferralTaskUpdateCommand && event.command.taskName != "taskStatus") {
                val srId = event.command.serviceRecipientId
                val recipient = serviceRecipientRepository.findById(srId).orElseThrow()
                val stId = serviceRecipientRepository.findConfigServiceTypeId(recipient.serviceAllocationId)
                val type = serviceTypeService.findOneDto(stId);
                val letterTemplate = type.getTaskDefByNameStrict(event.command.taskName)?.settings?.get("letterTemplateId") ?: return
                val letterData = type.getTaskDefByNameStrict(event.command.taskName)?.settings?.get("letterDataId") ?: return
                Assert.state(letterTemplate.length > 0, "letterTemplateId incorrect format")
                Assert.state(letterData.length > 0, "letterDataId incorrect format")
                val payload = LetterWebHookRequest(event.command.uuid.toString(), letterTemplate, letterData, srId)

                invokeWebhook(payload)
            }
        }

    }

    private fun invokeWebhook(payload: LetterWebHookRequest) {
        try {
            log.info("Sending webhook to ecco-letters:\n{}", payload.toString())

            // use a get to make the logs clearer
            var letterUri = UriComponentsBuilder.fromUriString(eccoLetterUrl!!)
                .queryParam("triggerId", payload.triggerId)
                .queryParam("srId", payload.srId.toString())
                .queryParam("templateId", payload.templateId)
                .queryParam("dataQuery", payload.dataId).build()

            val result = restTemplate.postForEntity(letterUri.toUri(), org.springframework.http.HttpEntity.EMPTY, String::class.java)
            Assert.state(result.statusCode.is2xxSuccessful, "Webhook ecco-letter failed: "+letterUri.toString())
        } catch (e: JsonProcessingException) {
            e.printStackTrace() // what can we do?
        }
    }
}
