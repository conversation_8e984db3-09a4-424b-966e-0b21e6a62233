<%@tag body-content="empty" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="security" uri="http://www.springframework.org/security/tags"  %>
<%@ taglib prefix="ecco" tagdir="/WEB-INF/tags" %>
<%@ taglib prefix="requireJs" tagdir="/WEB-INF/tags/requireJs"%>

<%@attribute name="serviceRecipientId" type="java.lang.Integer" required="true"%>
<%@attribute name="printView" type="java.lang.Boolean" required="false"%>

<%-- the JSP version - enhancing classes 'contact-wrapper' --%>
<requireJs:import modules="referral/enhanceReferralAssociatedContact"/>

<%-- this is the react-approach to 'contacts' --%>
<script type="text/javascript">
    require(['jquery', 'referral/ReferralContacts'],
        function ($, ReferralContacts) {
            $(function() {
                ReferralContacts.ReferralContactsEnhance($("#contacts-cards"), ${serviceRecipientId}, ${printView});
            });
        });
</script>
<div class="text-left top-gap-15 bottom-gap-15">
    <div id="contacts-cards">
    </div>
</div>
