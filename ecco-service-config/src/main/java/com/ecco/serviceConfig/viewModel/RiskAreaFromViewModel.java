package com.ecco.serviceConfig.viewModel;

import com.ecco.serviceConfig.dom.ActionGroup;
import com.ecco.serviceConfig.dom.OutcomeThreat;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

import static java.util.stream.Collectors.toList;

public final class RiskAreaFromViewModel implements Function<RiskAreaViewModel, OutcomeThreat> {

    private static final Function<RiskActionGroupViewModel, ActionGroup> actionGroupFromViewModel = new RiskActionGroupFromViewModel();

    @Nullable
    @Override
    public OutcomeThreat apply(@Nullable RiskAreaViewModel input) {
        if (input == null) {
            throw new NullPointerException("input RiskArea must not be null");
        }

        OutcomeThreat o = new OutcomeThreat();
        o.setId(input.id);
        o.setUuid(input.uuid);
        o.setName(input.name);

        o.setWeighting(0); // no longer used

        int order = 0;
        for (RiskActionGroupViewModel actionGroup : input.actionGroups) {
            actionGroup.orderby = order;
            order += 4; // to make inserting others later easy
        }

        o.addRisks(input.actionGroups.stream().map(actionGroupFromViewModel).collect(toList()));

        return o;
    }

}
